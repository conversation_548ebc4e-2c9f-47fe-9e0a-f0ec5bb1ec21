import { WebPlugin } from '@capacitor/core';

import type { ButterfliesPluginPlugin } from './definitions';

export class ButterfliesPluginWeb
  extends WebPlugin
  implements ButterfliesPluginPlugin
{
  async echo(options: { value: string }): Promise<{ value: string }> {
    console.log('ECHO', options);
    return options;
  }
  async shakeRegisterUser(args: { userId: string }): Promise<void> {
    return;
  }
  async shakeUnregisterUser(): Promise<void> {
    return;
  }
  async shakeUpdateUserMetadata(args: { metadata: object }): Promise<void> {
    return;
  }

  async shakeSetMetadata(args: { metadata: object }): Promise<void> {
    return;
  }
  async shakeClearMetadata(): Promise<void> {
    return;
  }
  async shakeSilentReport(args: { description: string }): Promise<void> {
    return;
  }
  async shakeShow(): Promise<void> {
    return;
  }
}
