import { Icons } from "common/assets/IconLoader";
import { isCapacitor } from "common/utils/Helper";
import ImgElement from "common/utils/ImgElement";

const NSFLContentWarning = () => {
  const iconSrc = isCapacitor()
    ? Icons.WarningMarkBigIcon
    : Icons.WarningMarkBigIcon.src;

  return (
    <div
      className={`flex flex-col items-center justify-center overflow-hidden rounded-[20px] bg-[#1d0707] dark:bg-[#2b0404] ${
        isCapacitor() ? "h-[200px] w-[155px]" : "h-[200px] w-[200px]"
      }`}
    >
      <div className="flex flex-col items-center justify-center px-3.25">
        <div
          className="mb-3 flex h-10 w-10 items-center justify-center rounded-full bg-[#ff3b3b]"
          style={{ boxShadow: "0px 2px 12px 0px rgba(255, 59, 59, 0.35)" }}
        >
          <ImgElement className="h-5 w-5" src={iconSrc} />
        </div>
        <p className="txt-black-p1 mb-2 font-semibold text-white">
          Content Blocked
        </p>
        <p className="txt-warnning text-center text-[#ff8686]">
          This content has been permanently blocked due to extreme or disturbing
          material.
        </p>
      </div>
      <div className="mt-4 flex items-center justify-center">
        <div
          className={`flex h-6 items-center justify-center rounded-full bg-[#3b0808] px-3 ${
            isCapacitor() ? "txt-semi-tiny" : "text-xs"
          }`}
        >
          <span className="text-[#ff6b6b]">NSFL Content</span>
        </div>
      </div>
    </div>
  );
};

export default NSFLContentWarning;
