import { useContext, useState, useEffect } from "react";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";

import playHaptics from "../../../mobile/src/utils/hapticsUtils";
import supabase from "common/libraries/supabaseClient";
import { UserContext } from "common/contexts/UserContext";
import "common/landing.css";
import { useTheme } from "common/contexts/ThemeContext";
import CustomButtonV2 from "common/utils/CustomButtonV2";
import { Icons } from "common/assets/IconLoader";
import OnboardingFlowLayout from "./../NewOnboarding/OnboardingFlowLayout";
import { isCapacitor } from "common/utils/Helper";
import Loading from "common/components/Loading";
import ImgElement from "common/utils/ImgElement";
import { trackEvent } from "common/utils/trackEvent";
import { wrappedError } from "common/utils/errorUtils";

export default function PhoneNumberVerifyV2() {
  const [verificationCode, setVerificationCode] = useState("");
  const [gotoVerify, setGotoVerfiy] = useState(false);
  const { loadUser, loadUserData, setIsAuthWaitingForNavigation } =
    useContext(UserContext);
  const [phoneNumber, setPhoneNumber] = useState("");
  const [count, setCount] = useState(30);
  const [flag, setFlag] = useState(false);
  const { theme } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [errorSection, setErrorSection] = useState();

  useEffect(() => {
    if (flag == true) {
      const timer = count > 0 && setInterval(() => setCount(count - 1), 1000);
      return () => clearInterval(timer);
    }
  }, [flag, count]);

  const handleInputCodeChange = (event: any) => {
    const value = event.target.value.replace(/\D/g, ""); // Remove non-digits
    if (value.length <= 6) {
      setVerificationCode(value);
    }
    if (value.length == 6) {
      onVerify(value);
    }
  };

  const onVerify = async (verifyCode) => {
    playHaptics("success");
    setIsLoading(true);

    try {
      const { data, error: otpError } = await supabase.auth.verifyOtp({
        phone: phoneNumber,
        token: verifyCode,
        type: "sms",
      });
      if (otpError) {
        const error = wrappedError(otpError);
        throw error;
      } else {
        trackEvent("auth.provider.completed", {
          provider: "phone",
        });

        ///////////////////////////////
        // TODO: we have a few places where we do something like this.
        // TODO: need a better way to orchestrate post-auth logic and navigation
        setIsAuthWaitingForNavigation("phone");
        loadUser();
        loadUserData(data?.user);
        ///////////////////////////////
      }
    } catch (error: any) {
      trackEvent("auth.provider.failed", {
        provider: "phone",
        error,
      });
      console.error("supabase.auth.verifyOtp error:", error);
      setVerificationCode("");
    } finally {
      setIsLoading(false);
    }
  };

  const sendVerification = async () => {
    trackEvent("auth.provider.started", { provider: "phone" });
    setIsLoading(true);
    const { error } = await supabase.auth.signInWithOtp({
      phone: phoneNumber,
    });
    if (error) {
      trackEvent("auth.provider.failed", {
        provider: "phone",
        error,
      });

      console.error("supabase.auth.signInWithOtp error:", error);
      setErrorSection("");
    } else {
      playHaptics("success");
      setFlag(true);
      setCount(30);
      setGotoVerfiy(true);
    }
    setIsLoading(false);
  };

  //allow all phone number
  const isValidAllNumber = (number) => {
    const regex = /^\d{7,15}$/;
    return regex.test(number);
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (
      event.key === "Enter" &&
      !isCapacitor() &&
      isValidAllNumber(phoneNumber)
    ) {
      event.preventDefault();
      sendVerification();
    }
  };

  if (!gotoVerify) {
    return (
      <>
        <OnboardingFlowLayout
          iconDark={
            isCapacitor() ? Icons.PhoneDarkIcon : Icons.PhoneDarkIcon.src
          }
          iconLight={
            isCapacitor()
              ? Icons.PhoneIconWhiteFilled
              : Icons.PhoneIconWhiteFilled.src
          }
          header={"Enter your phone number"}
          subtitle={
            "Verify your number. You’d be sent a verification code shortly"
          }
          errorSection={errorSection}
          inputSection={
            <>
              <div className="relative">
                <PhoneInput
                  country={"us"}
                  onlyCountries={["us", "ca"]}
                  buttonStyle={{
                    border: "none",
                    borderRadius: "500px",
                    paddingLeft: "8px",
                    backgroundColor: theme == "dark" ? "#FFFFFF0F" : "#F5F5F5",
                  }}
                  inputProps={{
                    autoFocus: true,
                  }}
                  inputStyle={{
                    fontFamily: "SF Pro Rounded",
                    width: "100%",
                    fontSize: "18px",
                    height: "50px",
                    color: theme == "dark" ? "white" : "black",
                    borderRadius: "500px",
                    border: "none",
                    backgroundColor: theme == "dark" ? "#FFFFFF0F" : "#F5F5F5",
                    boxShadow: "none",
                  }}
                  containerStyle={{
                    color: "black",
                    width: "100%",
                  }}
                  disableDropdown={true}
                  searchStyle={{ width: "100%" }}
                  value={phoneNumber}
                  onChange={(phone) => {
                    const inputPhone = phone[0] !== "1" ? `1${phone}` : phone;
                    setPhoneNumber(inputPhone);
                  }}
                  onKeyDown={handleKeyDown}
                />

                <div className="absolute right-[10px] top-0 pt-1.5">
                  <CustomButtonV2
                    type="primary"
                    size="small"
                    className="m-0 h-4.5 text-[12px]"
                    disabled={
                      !isLoading && isValidAllNumber(phoneNumber) ? false : true
                    }
                    onClick={() => sendVerification()}
                  >
                    <div className="absolute w-[24px]">
                      {isLoading ? (
                        <Loading
                          style={{ color: theme == "dark" ? "black" : "white" }}
                        />
                      ) : (
                        <ImgElement
                          src={
                            theme == "dark"
                              ? isCapacitor()
                                ? Icons.ArrowIcon
                                : Icons.ArrowIcon.src
                              : isCapacitor()
                                ? Icons.ArrowDarkIcon
                                : Icons.ArrowDarkIcon.src
                          }
                        />
                      )}
                    </div>
                  </CustomButtonV2>
                </div>
              </div>
            </>
          }
        />
      </>
    );
  }

  return (
    <>
      <OnboardingFlowLayout
        iconDark={isCapacitor() ? Icons.PhoneDarkIcon : Icons.PhoneDarkIcon.src}
        iconLight={isCapacitor() ? Icons.CodeIcon : Icons.CodeIcon.src}
        header={"Enter Code"}
        subtitle={"Please enter the code we sent to your phone number here"}
        errorSection={errorSection}
        inputSection={
          <>
            <div className="relative">
              <div className="relative">
                <input
                  className="w-full rounded-full border-none bg-[#F5F5F5] p-3 pl-6 text-[18px] font-medium outline-none focus:border-transparent focus:ring-0 dark:bg-[#FFFFFF0F]"
                  color="primary"
                  autoFocus
                  type="tel"
                  value={verificationCode}
                  onChange={handleInputCodeChange}
                  placeholder="••••••"
                  style={{ letterSpacing: "0.5rem" }}
                />
                <div className="absolute right-3 top-0">
                  <CustomButtonV2
                    type="primary"
                    size="small"
                    className="mt-2 h-4 text-[12px]"
                    disabled={
                      !isLoading && isValidAllNumber(phoneNumber) ? false : true
                    }
                    onClick={() => onVerify(verificationCode)}
                  >
                    <div className="absolute w-[24px]">
                      <ImgElement
                        src={
                          theme == "dark"
                            ? isCapacitor()
                              ? Icons.ArrowIcon
                              : Icons.ArrowIcon.src
                            : isCapacitor()
                              ? Icons.ArrowDarkIcon
                              : Icons.ArrowDarkIcon.src
                        }
                      />
                    </div>
                  </CustomButtonV2>
                </div>
              </div>
            </div>
          </>
        }
      />
    </>
  );
}
