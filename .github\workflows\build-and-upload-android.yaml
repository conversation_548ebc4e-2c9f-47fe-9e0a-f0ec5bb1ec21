name: Build Android and upload to Play Store Internal Testing

on:
  workflow_dispatch:
  workflow_call:
    inputs:
      commit_long_sha:
        required: true
        type: string
    secrets:
      ANDROID_KEYSTORE_FILE_BASE64:
        required: true
      KEYSTORE_STORE_PASSWORD:
        required: true
      KEYSTORE_KEY_ALIAS:
        required: true
      KEYSTORE_KEY_PASSWORD:
        required: true
      PLAY_CONFIG_JSON_BASE64:
        required: true

concurrency:
  group: android-${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-and-upload-android:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./packages/mobile
    steps:
      - name: output ref that will be used in the next step
        working-directory: .
        run: |
          echo "inputs.commit_long_sha: ${{ inputs.commit_long_sha }}"
          echo "github.head_ref: ${{ github.head_ref }}"
          echo "resulting ref: ${{ inputs.commit_long_sha || github.head_ref }}"

      - uses: actions/checkout@v4
        with:
          ref: ${{ inputs.commit_long_sha || github.head_ref }}

      - name: Use Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: npm

      - name: Install dependencies
        run: pushd ../.. && npm install && popd

      - name: Read build version
        id: read-build-version
        run: echo "BUILD_VERSION=$(npm run --silent set-version -- --read-build-version)" >> $GITHUB_OUTPUT

      - name: output build version in a separate step
        run: 'echo "read build version: ${{ steps.read-build-version.outputs.BUILD_VERSION }}"'

      - uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Ionic Capacitor Sync
        run: npm run sync

      - name: Ionic Capacitor Update
        run: npm run cap-update android

      - name: Setup java
        uses: actions/setup-java@v4
        with:
          distribution: "zulu"
          java-version: "17"

      - uses: ruby/setup-ruby@v1
        with:
          working-directory: "./packages/mobile"
          ruby-version: "3.0"
          bundler-cache: true

      - uses: maierj/fastlane-action@v3.1.0
        env:
          ANDROID_KEYSTORE_FILE_BASE64: ${{ secrets.ANDROID_KEYSTORE_FILE_BASE64 }}
          KEYSTORE_STORE_PASSWORD: ${{ secrets.KEYSTORE_STORE_PASSWORD }}
          KEYSTORE_KEY_ALIAS: ${{ secrets.KEYSTORE_KEY_ALIAS }}
          KEYSTORE_KEY_PASSWORD: ${{ secrets.KEYSTORE_KEY_PASSWORD }}
          PLAY_CONFIG_JSON_BASE64: ${{ secrets.PLAY_CONFIG_JSON_BASE64 }}
          BUILD_VERSION: ${{ steps.read-build-version.outputs.BUILD_VERSION }}
          GCS_SERVICE_ACCOUNT_KEY_BASE64: ${{ secrets.GCS_SERVICE_ACCOUNT_KEY_BASE64 }}
          GCS_BUCKET: ${{ vars.GCS_BUCKET }}
          GCS_PROJECT: ${{ vars.GCS_PROJECT }}
        with:
          subdirectory: "./packages/mobile"
          lane: android beta

      - name: Upload release bundle
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: android-artifacts
          path: ./packages/mobile/artifacts
          if-no-files-found: error
          retention-days: 10
      # FIXME: upload symbols to Sentry
