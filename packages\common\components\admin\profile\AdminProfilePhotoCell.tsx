import Link from "next/link";

import ResponsiveImage from "common/components/ResponsiveImage";
import { AdminGetDescription } from "common/components/admin/post";

interface PhotoCellProps {
  post: {
    id: number;
    media_url: string;
    caption: string;
    ai_caption: string;
  };
}

const AdminProfilePhotoCell = ({ post }: PhotoCellProps) => {
  return (
    <Link href={`/admin/posts/${post.id}/edit`}>
      <div className="solid flex flex-col items-center gap-2 rounded-md border bg-white p-4 hover:bg-stone-200">
        <div className="overflow-hidden rounded-md">
          <ResponsiveImage
            src={post.media_url}
            alt={post.caption}
            size={[200, 300]}
            autoHeight
            cover
          />
        </div>
        <p>{post.caption}</p>
        <p>{post.ai_caption && AdminGetDescription(post.ai_caption, 100)}</p>
      </div>
    </Link>
  );
};

export default AdminProfilePhotoCell;
