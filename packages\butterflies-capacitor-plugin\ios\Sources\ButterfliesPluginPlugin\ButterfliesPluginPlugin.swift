import Foundation
import Capacitor

/**
 * Please read the Capacitor iOS Plugin Development Guide
 * here: https://capacitorjs.com/docs/plugins/ios
 */
@objc(ButterfliesPluginPlugin)
public class ButterfliesPluginPlugin: CAPPlugin, CAPBridgedPlugin {
    public let identifier = "ButterfliesPluginPlugin"
    public let jsName = "ButterfliesPlugin"
    public let pluginMethods: [CAPPluginMethod] = [
        CAPPluginMethod(name: "echo", returnType: CAPPluginReturnPromise),
        CAPPluginMethod(name: "shakeRegisterUser", returnType: CAPPluginReturnPromise),
        CAPPluginMethod(name: "shakeUnregisterUser", returnType: CAPPluginReturnPromise),
        CAPPluginMethod(name: "shakeUpdateUserMetadata", returnType: CAPPluginReturnPromise),
        CAPPluginMethod(name: "shakeSetMetadata", returnType: CAPPluginReturnPromise),
        CAPPluginMethod(name: "shakeClearMetadata", returnType: CAPPluginReturnPromise),
        CAPPluginMethod(name: "shakeSilentReport", returnType: CAPPluginReturnPromise),
        CAPPluginMethod(name: "shakeShow", returnType: CAPPluginReturnPromise),
    ]
    private let implementation = ButterfliesPlugin()

    override public func load() {
        super.load()

        implementation.onLoad(capPlugin: self);
    }

    @objc func echo(_ call: CAPPluginCall) {
        let fromJSValue = call.getString("value") ?? ""
        let fromNativeValue = implementation.echo(fromJSValue)
        call.resolve([
            "value": fromNativeValue
        ])
    }

    @objc func shakeRegisterUser(_ call: CAPPluginCall) {
        if let userIdValue = call.getString("userId") {
            implementation.shakeRegisterUser(userId: userIdValue)
        }
        call.resolve()
    }

    @objc func shakeUnregisterUser(_ call: CAPPluginCall) {
        implementation.shakeUnregisterUser()
        call.resolve()
    }

    @objc func shakeUpdateUserMetadata(_ call: CAPPluginCall) {
        if let metadataValue = call.getObject("metadata") {
            implementation.shakeUpdateUserMetadata(metadata: metadataValue)
        }
        call.resolve()
    }

    @objc func shakeSetMetadata(_ call: CAPPluginCall) {
        if let metadataValue = call.getObject("metadata") {
            implementation.shakeSetMetadata(metadata: metadataValue)
        }
        call.resolve()
    }

    @objc func shakeClearMetadata(_ call: CAPPluginCall) {
        implementation.shakeClearMetadata()
        call.resolve()
    }

    @objc func shakeSilentReport(_ call: CAPPluginCall) {
        let description = call.getString("description")
        implementation.shakeSilentReport(description: description)
        call.resolve()
    }

    @objc func shakeShow(_ call: CAPPluginCall) {
        DispatchQueue.main.async { [weak self] in
            self?.implementation.shakeShow()
        }
        call.resolve()
    }

    
}
