import { Dispatch, useContext } from "react";
import { FiCheckCircle } from "react-icons/fi";

import { removeReport, adminActionLogs } from "common/libraries/adminApi";
import { AdminCommentReportType } from "common/components/admin/report/comment/AdminCommentReportItem";
import { UserContext } from "common/contexts/UserContext";

interface CommentReportDoneProps {
  reportID: number;
  setReports: Dispatch<React.SetStateAction<AdminCommentReportType[]>>;
}

const AdminCommentReportDoneButton = ({
  reportID,
  setReports,
}: CommentReportDoneProps) => {
  const { selectedProfile } = useContext(UserContext);

  /// handle "Done" button click
  const handleDoneReportClick = (reportID: number) => {
    /// add confirmation
    const isConfirmed = confirm("Are you sure this report is reviewed?");

    if (!isConfirmed) return;

    /// remove report
    removeReport({ id: reportID }).then(async (res) => {
      if (res.status === 200) {
        /// handle success
        setReports((prev: AdminCommentReportType[]) =>
          prev.filter((report) => report.id !== reportID),
        );
      } else {
        /// handle error
        console.log("Error occured while removing report");
      }

      /// log action
      await adminActionLogs({
        user_id: selectedProfile?.user_id,
        target: "comment_reports",
        action: "mark_as_reviewed",
        info: [
          {
            reportID: reportID,
          },
        ],
      });
    });
  };

  return (
    <button
      onClick={() => handleDoneReportClick(reportID)}
      className="flex items-center gap-1 rounded bg-lime-600 px-2 py-1 text-xs text-white"
    >
      <FiCheckCircle /> Done
    </button>
  );
};

export default AdminCommentReportDoneButton;
