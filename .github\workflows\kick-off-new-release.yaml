name: Kick off new release

on:
  push:
    branches:
      - dev
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  bump-version-and-tag-commit:
    uses: ./.github/workflows/bump-version-and-tag-commit.yaml
    permissions:
      # Give the default GITHUB_TOKEN write permission to commit and push the changed files back to the repository.
      contents: write

  ios:
    needs: bump-version-and-tag-commit
    uses: ./.github/workflows/build-and-upload-ios.yaml
    secrets: inherit
    with:
      commit_long_sha: ${{ needs.bump-version-and-tag-commit.outputs.commit_long_sha }}
  android:
    needs: bump-version-and-tag-commit
    uses: ./.github/workflows/build-and-upload-android.yaml
    secrets: inherit
    with:
      commit_long_sha: ${{ needs.bump-version-and-tag-commit.outputs.commit_long_sha }}
