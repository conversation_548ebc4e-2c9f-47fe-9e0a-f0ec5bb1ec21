import { useEffect, useMemo, useContext } from "react";
import { usePathname } from "next/navigation";
import { GoogleAuth } from "@codetrix-studio/capacitor-google-auth";
import { Browser } from "@capacitor/browser";
import { useHistory } from "react-router-dom";

import GoogleSignupButton from "./GoogleSignupButton";
import logoLight from "common/assets/<EMAIL>";
import { Icons } from "common/assets/IconLoader";
import AppleSignupButton from "./AppleSignupButton";
import CustomImage from "common/utils/CustomImage";
import Link from "common/utils/Link";
import { useTheme } from "common/contexts/ThemeContext";
import { UserContext } from "common/contexts/UserContext";
import { isCapacitor } from "common/utils/Helper";
import { AUTH_EXCLUDED_URLS, SITE_URL } from "common/config";
import CustomButtonV2 from "../../utils/CustomButtonV2";
import ImgElement from "common/utils/ImgElement";
import { trackEvent, trackExperiment } from "common/utils/trackEvent";
import { useRedirectAuth } from "common/state/redirectAuthState";
import { useFeatureFlagEnabled } from "common/utils/posthog";
import { setItemStorage } from "common/utils/localStorageWrappers";

export default function SignInCreateAccountScreenV3({
  isModal,
}: {
  isModal: Boolean;
}) {
  const { theme } = useTheme();
  const pathname = usePathname();
  const { setAuthData } = useContext(UserContext);
  const { setIsRedirectAuth } = useRedirectAuth();
  const router = useHistory();
  const isMobileAnonymousBrowsingEnabled = useFeatureFlagEnabled(
    "MOBILE_ANONYMOUS_BROWSING",
  );

  useEffect(() => {
    if (isMobileAnonymousBrowsingEnabled) {
      trackExperiment("anonymous browsing", "enabled");
    } else {
      trackExperiment("anonymous browsing", "disabled");
    }
  }, [isMobileAnonymousBrowsingEnabled]);

  const isAuthExcluded = useMemo(
    () => AUTH_EXCLUDED_URLS.some((uri) => new RegExp(uri).test(pathname)),
    [pathname],
  );

  if (isCapacitor()) {
    useEffect(() => {
      let pathname = window.location.pathname;
      if (!pathname?.includes("/auth/start")) {
        if (pathname !== "/settings" && pathname !== "/")
          setItemStorage("returnUrl", pathname);
        router.replace("/auth/start");
        setIsRedirectAuth(false);
      }
      GoogleAuth.initialize();
    }, []);
  }

  return (
    <div
      className={`flex h-full ${
        isModal ? "min-h-screen" : ""
      } items-center justify-center bg-default px-8 text-default sm:px-6 lg:px-8`}
      style={{
        fontFamily:
          '"SF Pro Rounded", "Helvetica Neue", "Helvetica", "Arial", sans-serif',
      }}
    >
      <div className="flex h-full w-full max-w-md flex-col space-y-6">
        <div className="flex-grow"></div>

        <div className="flex shrink-0 justify-center">
          <CustomImage
            src={isCapacitor() ? logoLight : logoLight.src}
            alt="Logo"
            height={80}
            width={104}
          />
        </div>
        <div className="flex-grow"></div>

        <div className="flex flex-col items-center justify-center space-y-1 text-center">
          <span className="txt-h1 text-default">Butterflies</span>
          <span className="txt-p4">Set your imagination free</span>
        </div>

        <div>
          <AppleSignupButton isNewStyle={true} />
          <GoogleSignupButton isNewStyle={true} />

          <div className={`h-[72px] opacity-100 transition-all duration-200`}>
            <Link
              onClick={() => {
                trackEvent("auth.provider.button_pressed", {
                  provider: "phone",
                });
              }}
              href={`/users/signin/phone${
                isAuthExcluded ? "" : "?returnUrl=" + pathname
              }`}
            >
              <CustomButtonV2 type="secondary">
                <ImgElement
                  className="absolute left-0 h-6 w-6"
                  src={
                    theme === "light" ? Icons.PhoneIcon : Icons.PhoneDarkIcon
                  }
                />
                Continue with Phone
              </CustomButtonV2>
            </Link>
          </div>
          <div
            className={`h-[72px] opacity-100 transition-all duration-500`}
            onClick={() => setAuthData({ type: "signup" })}
          >
            <Link
              onClick={() => {
                trackEvent("auth.provider.button_pressed", {
                  provider: "email",
                });
              }}
              href={`/users/signup/email${
                isAuthExcluded ? "" : "?returnUrl=" + pathname
              }`}
              routerDirection="forward"
            >
              <CustomButtonV2 type="secondary">
                <ImgElement
                  className="absolute left-0 h-6 w-6"
                  src={
                    theme === "light" ? Icons.EmailIcon : Icons.EmailDarkIcon
                  }
                />
                Continue with Email
              </CustomButtonV2>
            </Link>
          </div>

          {isMobileAnonymousBrowsingEnabled && (
            <>
              <div className="flex w-full justify-center pb-6 pt-3">
                <span className="txt-tiny">OR</span>
              </div>
              <Link href="/feed" className="w-full">
                <CustomButtonV2
                  type="secondary"
                  onClick={() => {
                    trackEvent("anonymous_browsing_feed.tapped");
                  }}
                >
                  <ImgElement
                    className="absolute left-0 h-6 w-6"
                    src={
                      theme === "light"
                        ? Icons.BrowsingIcon
                        : Icons.BrowsingDarkIcon
                    }
                  />
                  Browse Feed
                </CustomButtonV2>
              </Link>
            </>
          )}
        </div>

        <div className="m-2 pb-6 text-center text-[12px] text-[#808080] dark:text-white">
          By using Butterflies, you are agreeing with our <br />
          <span
            className="font-semibold"
            onClick={async () => {
              await Browser.open({ url: `${SITE_URL}/privacy` });
            }}
          >
            Privacy Policy
          </span>{" "}
          and{" "}
          <span
            className="font-semibold"
            onClick={async () => {
              await Browser.open({ url: `${SITE_URL}/terms` });
            }}
          >
            Terms of Service
          </span>
        </div>
      </div>
    </div>
  );
}
