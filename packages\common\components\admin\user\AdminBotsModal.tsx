import { Dispatch } from "react";
import { FiX } from "react-icons/fi";
import Link from "next/link";

import ModalBox from "common/components/Modal/ModalBox";
import Loading from "common/components/Loading";
import ResponsiveImage from "common/components/ResponsiveImage";
import { truncateName } from "common/components/Profile/SimilarProfileItem";
import { DEFAULT_AVATAR_URL } from "common/config/constants";

interface BotProfileProps {
  avatar_url: string;
  display_name: string;
  username: string;
}

interface BotProps {
  id: string;
  profiles: BotProfileProps;
}

interface ProfileProps {
  id: string;
  avatar_url: string;
  display_name: string;
  username: string;
}

interface ModalProps {
  username: string;
  isOpen: boolean;
  setIsOpen: Dispatch<boolean>;
  setProfiles: Dispatch<ProfileProps[]>;
  bots: BotProps[];
  setBots: Dispatch<BotProps[]>;
}

const AdminBotsModal = ({
  username,
  isOpen,
  setIsOpen,
  setProfiles,
  bots,
  setBots,
}: ModalProps) => {
  const ModalContents = () => {
    return (
      <>
        <div className="flex min-w-[350px] content-between items-center justify-between rounded-t-2xl border-b border-gray-200 bg-darkgray p-3 dark:border-white-800 dark:text-white">
          <div
            className="absolute right-3 cursor-pointer text-black dark:text-white"
            onClick={() => {
              setIsOpen(false);
              setProfiles([]);
              setBots([]);
            }}
          >
            <FiX size={24} />
          </div>
          <h2 className="flex w-full justify-center text-xl font-semibold capitalize text-black dark:text-white">
            Bots ({username})
          </h2>
          <div></div>
        </div>
        <div
          className="flex max-h-[50vh] flex-col overflow-y-auto border-2 border-none bg-darkgray p-2 text-black dark:text-white"
          onDragOver={(e) => e.preventDefault()}
        >
          {bots.length > 0 ? (
            bots?.map((bot: BotProps) => (
              <div
                className="w-full rounded-md px-2 hover:bg-slate-200 dark:hover:bg-slate-800"
                key={bot?.id}
              >
                <div className="flex items-center justify-between py-2">
                  <Link className="w-full" href={`/admin/ais/${bot.id}`}>
                    <div className="flex w-full items-center">
                      <ResponsiveImage
                        src={bot.profiles.avatar_url || DEFAULT_AVATAR_URL}
                        alt="Notification image"
                        size={[48, 48]}
                        rounded
                        cover
                      />

                      <div className="ml-3 grow break-all">
                        <div className="line-clamp-1 font-semibold text-black-100 dark:text-white-100">
                          {truncateName({
                            name: bot.profiles?.display_name,
                            limit: 30,
                          })}
                        </div>
                        <div className="text-sm text-white-500">
                          @
                          {truncateName({
                            name: bot.profiles?.username,
                            limit: 30,
                          })}
                        </div>
                      </div>
                    </div>
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <div className="flex min-h-[48vh] items-center justify-center">
              <Loading size={7} />
            </div>
          )}
        </div>
      </>
    );
  };

  return (
    <ModalBox
      isOpen={isOpen}
      setIsOpen={setIsOpen}
      ModalContents={ModalContents}
      maxWidth={500}
    />
  );
};

export default AdminBotsModal;
