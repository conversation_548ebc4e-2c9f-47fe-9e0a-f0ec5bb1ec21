import { useState } from "react";
import { FiX } from "react-icons/fi";

import ModalBox from "common/components/Modal/ModalBox";
import { getBotDetailAdmin } from "common/libraries/adminApi";

interface ProfileType {
  id: number;
  displayName: string;
}

const AdminProfileViewMoreModalButton = ({
  profile,
}: {
  profile: ProfileType;
}) => {
  const [bio, setBio] = useState("");
  const [appearance, setAppearance] = useState("");
  const [background, setBackground] = useState("");
  const [personality, setPersonality] = useState([]);
  const [characteristics, setCharacteristics] = useState("");
  const [modalVisibility, setModalVisibility] = useState(false);

  const onClick = async (e: any) => {
    e.preventDefault();

    try {
      const { data, error } = (
        await getBotDetailAdmin({ profile_id: profile.id })
      ).data;

      setModalVisibility(true);
      setBio(data?.bio ?? "");
      setAppearance(data?.description ?? "");
      setBackground(data?.background ?? "");
      setPersonality(data?.personality?.split(",") ?? []);
      setCharacteristics(data?.characteristics ?? "");

      if (error) {
        throw error;
      }
    } catch (error) {
      console.log("Error occurred: ", error);
    }
  };

  const ModalContent = () => (
    <>
      {/* Username */}
      <div className="flex min-w-[350px] content-between items-center justify-between rounded-t-2xl border-b border-gray-200 bg-darkgray p-3 dark:border-white-800 dark:text-white">
        <div
          className="absolute right-3 cursor-pointer text-black dark:text-white"
          onClick={() => {
            setModalVisibility(false);
            setAppearance("");
            setPersonality([]);
          }}
        >
          <FiX size={24} />
        </div>
        <h2 className="flex w-full justify-center text-xl font-semibold capitalize text-black dark:text-white">
          ({profile.displayName})
        </h2>
      </div>

      {/* Bio Info */}
      <div className="max-h-[800px] space-y-4 overflow-y-auto p-4">
        {/* Bio */}
        <div className="flex flex-col items-center space-y-2 dark:text-white">
          <b>Bio</b>
          {bio ? <div>{bio}</div> : <div>No Bio</div>}
        </div>

        {/* Appearance */}
        <div className="flex flex-col items-center space-y-2 dark:text-white">
          <b>Appearance</b>
          {appearance ? <div>{appearance}</div> : <div>No Appearance</div>}
        </div>

        {/* Background */}
        <div className="flex flex-col items-center space-y-2 dark:text-white">
          <b>Background</b>
          {background ? <div>{background}</div> : <div>No Background</div>}
        </div>

        {/* Characteristics */}
        <div className="flex flex-col items-center space-y-2 dark:text-white">
          <b>Characteristics</b>
          {characteristics ? (
            <div>{characteristics}</div>
          ) : (
            <div>No Characteristics</div>
          )}
        </div>

        {/* Personality */}
        <div className="flex flex-col items-center space-y-2 dark:text-white">
          <b>Personality</b>
          <div className="flex flex-wrap gap-2">
            {personality.length > 0 ? (
              personality.map((item, inx) => {
                return (
                  <div
                    key={inx}
                    className="rounded-full bg-black px-3 py-1 text-white"
                  >
                    {item}
                  </div>
                );
              })
            ) : (
              <div>No Personality</div>
            )}
          </div>
        </div>
      </div>
    </>
  );

  return (
    <>
      <div>
        <button onClick={(e) => onClick(e)} className="font-bold underline">
          View More
        </button>
      </div>

      {/* Bios : Modal */}
      <ModalBox
        isOpen={modalVisibility}
        setIsOpen={setModalVisibility}
        ModalContents={ModalContent}
        maxWidth={650}
      />
    </>
  );
};

export default AdminProfileViewMoreModalButton;
