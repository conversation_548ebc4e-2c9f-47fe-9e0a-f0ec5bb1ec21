"use client";
import { <PERSON>ik, Form, Field, ErrorMessage } from "formik";
import { useEffect, useState } from "react";

import { getBotDetailAdmin } from "common/libraries/adminApi";

const parseTemplateKeys = ({
  prompt,
  sendeeProfile,
  bot,
}: {
  prompt: any;
  sendeeProfile: any;
  bot: any;
}) => {
  // Regular expression to match all instances of {{anyText}}
  const regex = /\{\{(\w+)\}\}/g;
  let match;
  const keys: any = {};

  // Find all matches and add them to the keys object with empty strings as values
  while ((match = regex.exec(prompt)) !== null) {
    keys[match[1]] = "";
    if (match[1] == "char") keys[match[1]] = sendeeProfile?.display_name;
    if (match[1] == "bio") keys[match[1]] = bot?.bio;
  }

  return keys;
};

const PromptInsertsForm = ({
  prompt,
  sendeeProfile,
}: {
  prompt: any;
  sendeeProfile: any;
}) => {
  const [bot, setBot] = useState();

  const values = parseTemplateKeys({ prompt, sendeeProfile, bot });

  useEffect(() => {
    if (sendeeProfile?.profile_id) {
      const fetchProfile = async () => {
        try {
          const { data, error } = (
            await getBotDetailAdmin({ profile_id: sendeeProfile.profile_id })
          ).data;

          if (error) throw error;

          setBot(data);
        } catch (error) {
          console.error(error);
        }
      };

      fetchProfile();
    }
  }, [sendeeProfile]);

  const formFields = (values: any) => {
    return Object.keys(values).map((key) => (
      <div key={key} className="flex flex-col space-y-2">
        <label htmlFor={key} className="text-lg font-medium">
          {key}
        </label>
        <Field name={key} type="text" className="rounded border p-2" />
        <ErrorMessage
          name={key}
          component="div"
          className="text-sm text-red-500"
        />
      </div>
    ));
  };

  return (
    <Formik
      enableReinitialize={true}
      initialValues={values}
      onSubmit={async (values, { setSubmitting }) => {}}
    >
      {({ values }) => (
        <Form className="space-y-4 rounded bg-white p-4">
          {formFields(values)}
        </Form>
      )}
    </Formik>
  );
};

export default PromptInsertsForm;
