import React from "react";

interface TableProps {
  children: React.ReactNode;
}

export const Table = ({ children }: TableProps) => {
  return (
    <div className="relative overflow-x-auto">
      <table className="w-full text-left text-sm text-gray-500 dark:text-gray-400">
        {children}
      </table>
    </div>
  );
};

interface TableHeadProps {
  children: React.ReactNode;
}

Table.Head = ({ children }: TableHeadProps) => {
  return <thead className="bg-gray-50 dark:bg-gray-700">{children}</thead>;
};

interface TableHeaderProps {
  children: React.ReactNode;
}

Table.Header = ({ children }: TableHeaderProps) => {
  return (
    <th
      scope="col"
      className="px-6 py-3 text-xs font-medium uppercase text-gray-700 dark:text-gray-400"
    >
      {children}
    </th>
  );
};

interface TableBodyProps {
  children: React.ReactNode;
}

Table.Body = ({ children }: TableBodyProps) => {
  return <tbody>{children}</tbody>;
};

interface TableRowProps {
  children: React.ReactNode;
  onClick?: () => void;
}

Table.Row = ({ children, onClick }: TableRowProps) => {
  return (
    <tr 
      onClick={onClick}
      className={`border-b bg-white dark:border-gray-700 dark:bg-gray-800 ${onClick ? 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700' : ''}`}
    >
      {children}
    </tr>
  );
};

interface TableCellProps {
  children: React.ReactNode;
}

Table.Cell = ({ children }: TableCellProps) => {
  return <td className="px-6 py-4">{children}</td>;
};
