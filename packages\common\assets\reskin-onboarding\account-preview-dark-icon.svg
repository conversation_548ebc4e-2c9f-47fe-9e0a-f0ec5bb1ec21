<svg width="113" height="84" viewBox="0 0 113 84" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="22.5" width="68" height="47" rx="6" fill="#0e0e0e"/>
<rect x="22.75" y="0.25" width="67.5" height="46.5" rx="5.75" stroke="white" stroke-opacity="0.05" stroke-width="0.5"/>
<rect x="18.5" y="4" width="76" height="47" rx="6" fill="#0e0e0e"/>
<rect x="18.75" y="4.25" width="75.5" height="46.5" rx="5.75" stroke="white" stroke-opacity="0.05" stroke-width="0.5"/>
<g filter="url(#filter0_d_10021_187130)">
<rect x="12.5" y="8" width="88" height="60" rx="8" fill="#0e0e0e"/>
<rect x="12.75" y="8.25" width="87.5" height="59.5" rx="7.75" stroke="white" stroke-opacity="0.04" stroke-width="0.5"/>
</g>
<rect x="52" y="19" width="39" height="6" rx="3" fill="white" fill-opacity="0.1"/>
<rect x="52" y="29" width="39" height="6" rx="3" fill="white" fill-opacity="0.1"/>
<rect x="52" y="39" width="28" height="6" rx="3" fill="white" fill-opacity="0.05"/>
<defs>
<filter id="filter0_d_10021_187130" x="0.5" y="0" width="112" height="84" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_10021_187130"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_10021_187130" result="shape"/>
</filter>
</defs>
</svg>
