"use client";
import React, { Dispatch } from "react";

import <PERSON> from "common/utils/Link";
import ResponsiveImage from "common/components/ResponsiveImage";
import { DEFAULT_AVATAR_URL } from "common/config/constants";
import AdminPostReportDoneButton from "common/components/admin/report/post/AdminPostReportDoneButton";
import AdminPostReportNSFWButton from "common/components/admin/report/post/AdminPostReportNSFWButton";
import AdminPostReportNSFLButton from "common/components/admin/report/post/AdminPostReportNSFLButton";

/// Type of post report
export interface AdminPostReportType {
  id: number;

  /// post
  post: {
    id: number;
    imgURL: string;
    description: string;
    nsfw: string;
    nsfl: boolean;
    tags: string[];
  };

  /// poster
  poster: {
    id: number;
    avatarURL: string;
    name: string;
    username: string;
    description: string;
    nsfw: string;
    nsfl: boolean;
  };

  /// reporter
  reporter: {
    id: number;
    name: string;
    username: string;
    avatarURL: string;
  };

  /// reason
  types: string[];
  userComment: string;
}

const AdminPostReportItem = ({
  report,
  setReports,
}: {
  report: AdminPostReportType;
  setReports: Dispatch<React.SetStateAction<AdminPostReportType[]>>;
}) => {
  return (
    <div className="mx-auto flex w-full max-w-full flex-col justify-start bg-white text-xs dark:bg-black-100">
      <div className="mt-3 w-full space-y-4 overflow-hidden rounded-lg border border-gray-200 bg-white text-left text-base font-medium text-gray-900 shadow-sm hover:shadow-lg focus-visible:ring focus-visible:ring-gray-500/75 dark:border-gray-500 dark:bg-slate-900 dark:text-white">
        <div className="flex w-full flex-col lg:flex-row">
          <div className="flex w-full flex-row sm:w-2/3">
            <div className="relative h-72 w-56 flex-shrink-0">
              <div className="h-full w-full overflow-hidden">
                <ResponsiveImage
                  src={report.post.imgURL}
                  alt={"Post image"}
                  size={[224, 288]}
                />
              </div>
            </div>
            <div className="flex flex-1 flex-col justify-between border-r">
              <div className="flex h-full w-full flex-col p-3">
                <div className="flex flex-row justify-between gap-4">
                  <div className="flex h-full flex-col justify-between">
                    {/* Post ID */}
                    <div className="flex gap-1">
                      <b>ID:</b>
                      <Link
                        href={`/admin/posts/${report.post.id}/edit`}
                        as={`/admin/posts/${report.post.id}/edit`}
                        className="link"
                      >
                        {report.post.id}
                      </Link>
                    </div>

                    {/* Post Description */}
                    <div className="overflow-y-auto">
                      {"Description: "}
                      <span className="text-gray-500">
                        {report.post.description}
                      </span>
                    </div>
                  </div>

                  {/* Poster Profile */}
                  <div className="flex h-fit w-fit min-w-[20rem] items-center gap-2 overflow-hidden rounded-l-[2.75rem] rounded-r-xl border border-red-200 bg-red-50 p-1 pr-4">
                    {/* Poster Avatar */}
                    <div className="min-w-10 h-20 w-20 flex-shrink-0 justify-center overflow-hidden rounded-full">
                      <ResponsiveImage
                        src={report.poster.avatarURL || DEFAULT_AVATAR_URL}
                        alt={"Poster avatar"}
                        size={[80, 80]}
                      />
                    </div>
                    <div className="z-10 flex flex-col gap-0.5">
                      {/* Poster ID */}
                      <div className="flex items-center gap-1">
                        <p>ID:</p>
                        <Link
                          href="/admin/profiles/[id]"
                          as={`/admin/profiles/${report.poster.id}`}
                          className="link"
                        >
                          {report.poster.id}
                        </Link>

                        {/* Poster Profile NSFW */}
                        {report.poster.nsfw === "nsfw" && (
                          <div className="ml-2 rounded-xl border border-red-400 bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-gray-700 dark:text-red-400">
                            NSFW
                          </div>
                        )}

                        {/* Poster Profile NSFL */}
                        {report.poster.nsfl == true && (
                          <div className="rounded-xl border border-[#ff4747] bg-[#fcff3a] px-2.5 py-0.5 text-xs font-medium text-[#ff4747] dark:bg-gray-700 dark:text-red-500">
                            NSFL
                          </div>
                        )}
                      </div>

                      {/* Poster Name */}
                      <div className="flex gap-1">
                        <p>Name:</p>
                        <p className="line-clamp-1 max-w-[48rem]">
                          {report?.poster.name}
                        </p>
                      </div>

                      <div className="flex items-center gap-1">
                        {/* Poster Username */}
                        <p>Username:</p>
                        <Link
                          href="/users/[id]"
                          as={`/users/${report.poster.username}`}
                          className="link"
                        >
                          {report.poster.username}
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col items-end justify-between gap-2 p-3 lg:flex-row">
                {/* Post tags */}
                <div className="flex flex-col items-start gap-2 lg:flex-row">
                  {report.post.tags.map((tag: string, index: number) => (
                    <div
                      className="rounded bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-900 dark:text-gray-300"
                      key={index}
                    >
                      {tag}
                    </div>
                  ))}
                </div>

                <div className="flex flex-col items-end gap-2 lg:flex-row">
                  {/* Mark as NSFW : Button */}
                  <AdminPostReportNSFWButton
                    postID={report.post.id}
                    nsfw={report.post.nsfw}
                    setReports={setReports}
                  />

                  {/* Mark as NSFL : Button */}
                  <AdminPostReportNSFLButton
                    postID={report.post.id}
                    nsfl={report.post.nsfl}
                    setReports={setReports}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Reporter */}
          <div className="flex flex-col justify-between gap-2 p-3 sm:w-1/3">
            <div className="flex flex-col gap-2">
              {/* Reporter Profile */}
              <div className="relative flex h-fit w-fit min-w-[20rem] items-center gap-2 overflow-hidden rounded-l-[2.75rem] rounded-r-xl border border-blue-200 bg-blue-50 p-1 pr-4">
                {/* Reporter Avatar */}
                <div className="min-w-10 h-20 w-20 flex-shrink-0 justify-center overflow-hidden rounded-full">
                  <ResponsiveImage
                    src={report.reporter.avatarURL || DEFAULT_AVATAR_URL}
                    alt={"Reporter avatar"}
                    size={[80, 80]}
                  />
                </div>

                <div className="flex flex-col gap-0.5">
                  {/* Reporter ID */}
                  <div className="flex gap-1">
                    <p>ID:</p>
                    <Link
                      href="/admin/profiles/[id]"
                      as={`/admin/profiles/${report.reporter.id}`}
                      className="link"
                    >
                      {report.reporter.id}
                    </Link>
                  </div>

                  {/* Reporter Name */}
                  <div className="flex gap-1">
                    <p>Name:</p>
                    <p className="line-clamp-1 max-w-[48rem]">
                      {report?.reporter.name}
                    </p>
                  </div>

                  {/* Reporter Username */}
                  <div className="flex gap-1">
                    <p>Username:</p>
                    <Link
                      href="/users/[id]"
                      as={`/users/${report.reporter.username}`}
                      className="link"
                    >
                      {report.reporter.username}
                    </Link>
                  </div>
                </div>
              </div>

              {/* Reason Comment */}
              <p className="w-fit rounded-md bg-red-200 px-3">
                {report.userComment}
              </p>

              {/* Reason Types */}
              <div className="flex max-w-full flex-wrap gap-2">
                {report.types.map((type: string, index: number) => {
                  return (
                    <div
                      key={index}
                      className="w-fit rounded-full bg-gray-200 px-3"
                    >
                      {type}
                    </div>
                  );
                })}
              </div>
            </div>

            <div className="flex w-full justify-end">
              {/* Done : Button */}
              <AdminPostReportDoneButton
                reportID={report.id}
                setReports={setReports}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminPostReportItem;
