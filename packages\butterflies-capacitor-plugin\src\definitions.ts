import type { Plugin } from "@capacitor/core";

export interface ButterfliesPluginPlugin extends Plugin {
  echo(args: { value: string }): Promise<{ value: string }>;
  shakeRegisterUser(args: { userId: string }): Promise<void>;
  shakeUnregisterUser(): Promise<void>;
  shakeUpdateUserMetadata(args: { metadata: object }): Promise<void>;
  shakeSetMetadata(args: { metadata: object }): Promise<void>;
  shakeClearMetadata(): Promise<void>;
  shakeSilentReport(args: { description: string }): Promise<void>;
  shakeShow(): Promise<void>;
}
