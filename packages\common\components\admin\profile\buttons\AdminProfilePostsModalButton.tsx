import { useState } from "react";
import { FiX } from "react-icons/fi";

import Loading from "common/components/Loading";
import ModalBox from "common/components/Modal/ModalBox";
import ResponsiveImage from "common/components/ResponsiveImage";
import { fetchPostUrlsWithProfileId } from "common/libraries/api";
import { wrappedError } from "common/utils/errorUtils";

interface PostType {
  media_url: string;
}

interface ProfileType {
  id: number;
  displayName: string;
  totalPostsCount: number;
  publicPostsCount: number;
}

const AdminProfilePostsModalButton = ({
  profile,
}: {
  profile: ProfileType;
}) => {
  const [posts, setPosts] = useState<PostType[]>([]);
  const [modalVisibility, setModalVisibility] = useState(false);

  const fetchPosts = async () => {
    try {
      const response = await fetchPostUrlsWithProfileId({
        profile_id: profile?.id,
      });
      const { data, error } = response.data;

      if (error) {
        throw wrappedError(error, "Error fetching posts url with profile_id");
      }

      setPosts(data);
    } catch (error) {
      console.log("Error occured while fetching posts: ", error);
    }
  };

  const onClick = async () => {
    setPosts([]);
    setModalVisibility(true);
    fetchPosts();
  };

  const ModalContent = () => (
    <>
      <div className="flex min-w-[350px] content-between items-center justify-between rounded-t-2xl border-b border-gray-200 bg-darkgray p-3 dark:border-white-800 dark:text-white">
        <div
          className="absolute right-3 cursor-pointer text-black dark:text-white"
          onClick={() => {
            setModalVisibility(false);
            setPosts([]);
          }}
        >
          <FiX size={24} />
        </div>
        <h2 className="flex w-full justify-center text-xl font-semibold capitalize text-black dark:text-white">
          ({profile.displayName})
        </h2>
        <div></div>
      </div>
      {posts?.length > 0 ? (
        <div
          className="grid max-h-[50vh] grid-cols-1 gap-2 overflow-y-auto border-2 border-none bg-darkgray p-2 text-black dark:text-white lg:grid-cols-2 xl:grid-cols-3"
          onDragOver={(e) => e.preventDefault()}
        >
          {posts?.length > 0 &&
            posts?.map((post: PostType, index) => (
              <div
                className="flex w-full justify-center rounded-md px-2 hover:bg-slate-200"
                key={index}
              >
                <ResponsiveImage
                  src={post.media_url}
                  alt={"Post"}
                  size={[210, 210]}
                />
              </div>
            ))}
        </div>
      ) : (
        <div className="flex min-h-[48vh] items-center justify-center">
          <Loading size={7} />
        </div>
      )}
    </>
  );

  return (
    <>
      <button
        onClick={() => onClick()}
        disabled={!(profile.totalPostsCount > 0)}
        className={`${
          profile.totalPostsCount ? "underline" : "hover:cursor-default"
        } font-bold`}
      >
        {`Posts(All / Public): ${profile.totalPostsCount}/${profile.publicPostsCount}`}
      </button>

      {/* Posts : Modal */}
      <ModalBox
        isOpen={modalVisibility}
        setIsOpen={setModalVisibility}
        ModalContents={ModalContent}
        maxWidth={650}
      />
    </>
  );
};

export default AdminProfilePostsModalButton;
