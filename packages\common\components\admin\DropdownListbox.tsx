"use client";
import React, { Fragment } from "react";
import { Listbox, Transition } from "@headlessui/react";
import { CheckIcon, ChevronUpDownIcon } from "@heroicons/react/24/outline";

const DropdownListbox = ({
  selectedValue,
  setSelectedValue,
  valueList,
  placeholder,
}: {
  selectedValue: any;
  setSelectedValue: any;
  valueList: any[];
  placeholder: string;
}) => {
  return (
    /// (reference link - https://headlessui.com/react/listbox)
    <Listbox value={selectedValue} onChange={setSelectedValue}>
      <div className="relative">
        <Listbox.Button className="relative w-full rounded-lg border border-gray-300 bg-gray-50 py-2 pl-3 pr-10 text-left shadow-md focus:border-gray-500 focus:ring-gray-500 focus-visible:border-indigo-500 focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 sm:text-sm">
          <span className="block truncate">
            {selectedValue.value == "" && placeholder
              ? placeholder
              : selectedValue.label}
          </span>
          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
            <ChevronUpDownIcon
              className="h-5 w-5 text-gray-400"
              aria-hidden="true"
            />
          </span>
        </Listbox.Button>
        <Transition
          as={Fragment}
          leave="transition ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none dark:bg-gray-800 sm:text-sm">
            {valueList.map((value: any, index: number) => (
              <Listbox.Option
                key={index}
                className={({ active }) =>
                  `relative cursor-default select-none py-2 pl-8 pr-4 ${
                    active
                      ? "bg-gray-100 text-black dark:bg-gray-700 dark:text-white"
                      : "text-gray-900 dark:text-white"
                  }`
                }
                value={value}
              >
                {({ selected }) => (
                  <>
                    <span
                      className={`block truncate ${
                        selected ? "font-bold" : "font-normal"
                      }`}
                    >
                      {value.label}
                    </span>
                    {selected ? (
                      <span className="absolute inset-y-0 left-0 flex items-center pl-1 text-gray-600 dark:text-white">
                        <CheckIcon className="h-5 w-5" aria-hidden="true" />
                      </span>
                    ) : null}
                  </>
                )}
              </Listbox.Option>
            ))}
          </Listbox.Options>
        </Transition>
      </div>
    </Listbox>
  );
};

export default DropdownListbox;
