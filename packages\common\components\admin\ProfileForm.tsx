"use client";
import { Formik, Form, Field, ErrorMessage } from "formik";
import { useRouter } from "next/navigation";
import * as Yup from "yup";
import { updateProfile } from "../../libraries/api";
import { selectedProfileAtom } from "common/state/selectedProfile";
import { useAtomValue } from "jotai";
import { deleteProfile } from "../../libraries/adminApi";
import { adminActionLogs } from "../../libraries/adminApi";

const BotStatusOptions = [
  { value: "online", label: "Online" },
  { value: "away", label: "Away" },
  { value: "offline", label: "Offline" },
  // Add more options as needed
];

const ProfileForm = ({ profile }) => {
  const router = useRouter();
  const selectedProfile = useAtomValue(selectedProfileAtom);

  const handleDeleteProfile = async (e: any) => {
    e.preventDefault();

    if (confirm("Are you sure? this cannot be undone")) {
      const deleteResult = await deleteProfile({ profileId: profile?.id });
      const { data: profileData = null, error: profileError = null } =
        deleteResult.data;
      if (!profileError) {
        router.push("/admin/profiles");
      }
    }
  };

  return (
    <Formik
      initialValues={{
        username: profile.username || "",
        user_id: profile.user_id || null,
        description: profile.description || "",
        display_name: profile?.display_name || "",
        follower_count: profile.follower_count || 0,
        following_count: profile.following_count || 0,
        nsfw: profile.nsfw || null,
        visibility: profile.visibility || null,
      }}
      validationSchema={Yup.object({
        username: Yup.string().required("Required"),
        user_id: Yup.string().notRequired(),
        description: Yup.string().notRequired(),
        display_name: Yup.string().notRequired(),
        follower_count: Yup.number().integer().min(0).required("Required"),
        following_count: Yup.number().integer().min(0).required("Required"),
        nsfw: Yup.string()
          .oneOf(["normal", "nsfw"], "Invalid NSFW")
          .required("Required"),
        visibility: Yup.string()
          .oneOf(
            ["draft", "hidden", "private", "public", "unlisted", "archived"],
            "Invalid Visibility",
          )
          .required("Required"),
      })}
      onSubmit={async (values, { setSubmitting }) => {
        try {
          let names: string[] | null = profile?.old_usernames;
          if (values.username !== profile.username) {
            names = [...(profile?.old_usernames ?? []), values.username];
            names = names.filter(
              (name, index) => names?.indexOf(name) === index,
            );
          }

          const updateResult = await updateProfile({
            updateContents: {
              username: values.username,
              user_id: values.user_id,
              description: values.description,
              display_name: values?.display_name,
              follower_count: values.follower_count,
              following_count: values.following_count,
              nsfw: values.nsfw,
              visibility: values.visibility,
              old_usernames: names,
            },
            profileId: profile.id,
          });
          const { data: profileData = null, error: profileError = null } =
            updateResult.data;

          if (profileError) throw profileError;
          window.location.reload();
        } catch (error) {
          alert("Failed to update profile: " + error?.message);
        } finally {
          await adminActionLogs({
            target: "profiles",
            action: "edit",
            info: [
              {
                table: "profiles",
                id: profile?.id,
                old: {
                  username: profile?.username,
                  user_id: profile?.user_id,
                  description: profile?.description,
                  display_name: profile?.display_name,
                  follower_count: profile?.follower_count,
                  following_count: profile?.following_count,
                  nsfw: profile?.nsfw,
                  visibility: profile?.visibility,
                },
                new: {
                  username: values.username,
                  user_id: values.user_id,
                  description: values.description,
                  display_name: values?.display_name,
                  follower_count: values.follower_count,
                  following_count: values.following_count,
                  nsfw: values.nsfw,
                  visibility: values.visibility,
                },
              },
            ],
          });

          setSubmitting(false);
        }
      }}
    >
      {() => (
        <Form className="flex flex-col gap-4 rounded border border-gray-300 bg-white p-4 dark:bg-slate-900">
          {/* Display Name */}
          <div className="flex items-center gap-4">
            <div className="flex w-32 justify-end">
              <label
                htmlFor="display_name"
                className="text-lg font-medium text-black dark:text-white"
              >
                Display Name
              </label>
            </div>
            <Field
              name="display_name"
              type="text"
              className="flex-1 rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
              disabled={selectedProfile?.users?.role !== "admin"}
            />
          </div>

          {/* Username */}
          <div className="flex items-center gap-4">
            <div className="flex w-32 justify-end">
              <label
                htmlFor="username"
                className="text-lg font-medium text-black dark:text-white"
              >
                Username
              </label>
            </div>
            <Field
              name="username"
              type="text"
              className="flex-1 rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
              disabled={selectedProfile?.users?.role !== "admin"}
            />
            <ErrorMessage
              name="username"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          {/* User ID */}
          <div className="flex items-center gap-4">
            <div className="flex w-32 justify-end">
              <label
                htmlFor="user_id"
                className="text-lg font-medium text-black dark:text-white"
              >
                User ID
              </label>
            </div>
            <Field
              name="user_id"
              type="text"
              className="flex-1 rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
              disabled
            />
            <ErrorMessage
              name="user_id"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label
              htmlFor="description"
              className="text-lg font-medium text-black dark:text-white"
            >
              Description
            </label>
            <Field
              name="description"
              as="textarea"
              type="text"
              className="h-20 rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
              disabled={selectedProfile?.users?.role !== "admin"}
            />
            <ErrorMessage
              name="description"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex flex-col items-end gap-4">
              {/* Follower Count */}
              <div className="flex gap-2">
                <div className="flex items-center">
                  <label
                    htmlFor="follower_count"
                    className="text-lg font-medium text-black dark:text-white"
                  >
                    Follower Count
                  </label>
                </div>
                <Field
                  name="follower_count"
                  type="number"
                  disabled={true}
                  className="w-20 rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
                />
              </div>

              {/* Following Count */}
              <div className="flex gap-2">
                <div className="flex items-center">
                  <label
                    htmlFor="following_count"
                    className="text-lg font-medium text-black dark:text-white"
                  >
                    Following Count
                  </label>
                </div>
                <Field
                  name="following_count"
                  type="number"
                  disabled={true}
                  className="w-20 rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
                />
              </div>
            </div>

            <div className="flex flex-col items-end gap-4">
              {/* Visibility */}
              <div className="flex items-center gap-2">
                <label
                  htmlFor="nsfw"
                  className="text-lg font-medium text-black dark:text-white"
                >
                  Visibility
                </label>
                <Field
                  name="visibility"
                  as="select"
                  disabled={selectedProfile?.users?.role !== "admin"}
                  className="rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
                >
                  <option value="draft">Draft</option>
                  <option value="hidden">Hidden</option>
                  <option value="private">Private</option>
                  <option value="public">Public</option>
                  <option value="unlisted">Unlisted</option>
                  <option value="archived">Archived</option>
                </Field>
                <ErrorMessage
                  name="visibility"
                  component="div"
                  className="text-sm text-red-500"
                />
              </div>

              {/* NSFW */}
              <div className="flex items-center gap-2">
                <label
                  htmlFor="nsfw"
                  className="text-lg font-medium text-black dark:text-white"
                >
                  NSFW
                </label>
                <Field
                  name="nsfw"
                  as="select"
                  disabled={selectedProfile?.users?.role !== "admin"}
                  className="rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
                >
                  <option value="normal">Normal</option>
                  <option value="nsfw">NSFW</option>
                </Field>
                <ErrorMessage
                  name="nsfw"
                  component="div"
                  className="text-sm text-red-500"
                />
              </div>
            </div>
          </div>

          <div className="flex flex-wrap justify-center">
            <div className="mr-4 mt-4 flex justify-center gap-3">
              <button
                type="submit"
                className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 disabled:cursor-not-allowed disabled:bg-blue-400"
                disabled={selectedProfile?.users?.role !== "admin"}
              >
                Update
              </button>
            </div>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default ProfileForm;
