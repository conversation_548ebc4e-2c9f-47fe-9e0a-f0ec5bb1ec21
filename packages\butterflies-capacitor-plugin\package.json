{"name": "@butterflies/capacitor-plugin", "version": "0.0.1", "description": "Tightly coupled plugin to bridge functionality to native", "main": "dist/plugin.cjs.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "unpkg": "dist/plugin.js", "files": ["android/src/main/", "android/build.gradle", "dist/", "ios/Sources", "ios/Tests", "Package.swift", "ButterfliesCapacitorPlugin.podspec"], "author": "", "license": "None", "repository": {"type": "git", "url": "git+https://github.com/butterflies-ai/butterflies-capacitor-plugin.git"}, "bugs": {"url": "https://github.com/butterflies-ai/butterflies-capacitor-plugin/issues"}, "keywords": ["capacitor", "plugin", "native"], "scripts": {"verify": "npm run verify:ios && npm run verify:android && npm run verify:web", "verify:ios": "xcodebuild -scheme ButterfliesCapacitorPlugin -destination generic/platform=iOS", "verify:android": "cd android && ./gradlew clean build test && cd ..", "verify:web": "npm run build", "lint": "npm run eslint && npm run prettier -- --check && npm run swiftlint -- lint", "fmt": "npm run eslint -- --fix && npm run prettier -- --write && npm run swiftlint -- --fix --format", "eslint": "eslint . --ext ts", "prettier": "prettier \"**/*.{css,html,ts,js,java}\"", "swiftlint": "node-swiftlint", "docgen": "docgen --api ButterfliesPluginPlugin --output-readme README.md --output-json dist/docs.json", "build": "npm run clean && npm run docgen && tsc && rollup -c rollup.config.js", "clean": "rimraf ./dist", "watch": "tsc --watch", "prepublishOnly": "npm run build"}, "devDependencies": {"@capacitor/android": "^6.0.0", "@capacitor/core": "^6.0.0", "@capacitor/docgen": "^0.2.2", "@capacitor/ios": "^6.0.0", "@ionic/eslint-config": "^0.4.0", "@ionic/prettier-config": "^1.0.1", "@ionic/swiftlint-config": "^1.1.2", "eslint": "^8.57.0", "prettier": "~2.3.0", "prettier-plugin-java": "~1.0.2", "rimraf": "^3.0.2", "rollup": "^2.32.0", "swiftlint": "^1.0.1", "typescript": "~4.1.5"}, "peerDependencies": {"@capacitor/core": "^6.0.0"}, "prettier": "@ionic/prettier-config", "swiftlint": "@ionic/swiftlint-config", "eslintConfig": {"extends": "@ionic/eslint-config/recommended"}, "capacitor": {"ios": {"src": "ios"}, "android": {"src": "android"}}}