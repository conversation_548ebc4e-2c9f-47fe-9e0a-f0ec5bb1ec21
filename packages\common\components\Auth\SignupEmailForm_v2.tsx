"use client";
import { useState, useContext, useCallback, useMemo } from "react";
import { debounce } from "lodash";

import CustomInput from "common/elements/CustomInput";
import { Icons } from "common/assets/IconLoader";
import { UserContext } from "common/contexts/UserContext";
import OnboardingFlowLayout from "../NewOnboarding/OnboardingFlowLayout";
import { checkEmailExists } from "common/libraries/api";
import CustomImage from "common/utils/CustomImage";
import { isCapacitor } from "common/utils/Helper";

export default function SignupEmailForm_v2() {
  const { authData, setAuthData } = useContext(UserContext);

  const [hasProcessedEmail, setHasProcessedEmail] = useState<boolean>(false);
  const [doesEmailExist, setDoesEmailExist] = useState<boolean>();
  const [isValid, setIsValid] = useState(false);
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleInputChange = (event) => {
    let value = event.target.value;
    setHasProcessedEmail(false);

    setEmail(value);

    const valid = isEmailValid(value);
    setIsValid(valid);
  };

  const emailCheck = async (email: string) => {
    setIsLoading(true);

    /// availability check
    try {
      const { data } = await checkEmailExists(email);

      setDoesEmailExist(data.exists);

      setHasProcessedEmail(true);
    } catch (error) {
      console.error(error);
    }

    setHasProcessedEmail(true);
    setIsLoading(false);
  };

  const debouncedEmailCheck = useCallback(
    debounce((email) => emailCheck(email), 800),
    [],
  );

  const isEmailValid = (emailValue) => {
    try {
      /// 1. The local part (before the @) contains only letters, digits, and certain special characters.
      ///  - does not contain consecutive dots.
      ///  - does not start with a dot.
      ///  - does not end with a dot.
      /// 2. The domain part (after the @) contains only letters, digits, hyphens, and periods.
      /// 3. The top-level domain (after the last dot) has at least two letters.
      const regEx = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

      const localPart = emailValue?.split("@")[0] || "";
      // Check if the email local part starts or ends with a dot
      if (localPart.startsWith(".") || localPart.endsWith(".")) {
        return false;
      }

      // Check if the email local part contains consecutive dots
      if (localPart == "" || localPart.includes("..")) {
        return false;
      }

      // Validate the email against the regex
      if (!regEx.test(emailValue)) {
        return false;
      }

      debouncedEmailCheck(emailValue);
      return true; // Email is valid
    } catch (error) {
      return false; // Email is invalid
    }
  };

  const errorSectionElement = useMemo(() => {
    if (!hasProcessedEmail) return <></>;

    return (
      <div className="mb-4 ml-4 flex space-x-1">
        <CustomImage
          src={
            isCapacitor()
              ? Icons.FilledCheckmarkIcon
              : Icons.FilledCheckmarkIcon.src
          }
          className="mr-1"
          width={isCapacitor() ? null : 20}
          height={isCapacitor() ? null : 24}
        />
        {doesEmailExist && (
          <span className="font-normal text-[#808080]">
            {"Welcome back! "}

            <span className="font-semibold text-black dark:text-white">
              Sign in
            </span>
          </span>
        )}
        {!doesEmailExist && (
          <span className="font-normal text-[#808080]">
            {"Continue to "}

            <span className="font-semibold text-black dark:text-white">
              sign up
            </span>
          </span>
        )}
      </div>
    );
  }, [doesEmailExist, hasProcessedEmail]);

  return (
    <OnboardingFlowLayout
      iconDark={
        isCapacitor()
          ? Icons.EmailDarkIconFilled
          : Icons.EmailDarkIconFilled.src
      }
      iconLight={
        isCapacitor() ? Icons.EmailIconFilled : Icons.EmailIconFilled.src
      }
      header={"What’s your email?"}
      subtitle={
        "We'll send verification codes and allow you reset your password"
      }
      errorSection={errorSectionElement}
      inputSection={
        <>
          <CustomInput
            _autoFocus={true}
            keepFocused={false}
            loading={isLoading}
            value={email}
            placeholder="Email Address"
            onChange={handleInputChange}
            type="email"
            handleOk={() => {
              setAuthData({ ...authData, email: email });
            }}
            path={`/users/signup/pass?email=${encodeURIComponent(
              email,
            )}&signin=${doesEmailExist}`}
            btnDisabled={!email || !isValid || !hasProcessedEmail || isLoading}
            maxLength={50}
          />
        </>
      }
    />
  );
}
