"use client";
import React, { Dispatch } from "react";
import dayjs from "dayjs";
import {
  FiDelete,
  FiEdit,
  <PERSON>Flag,
  FiAlertOctagon,
  FiRotateCw,
  FiTrash2,
} from "react-icons/fi";
import Link from "next/link";
import { useAtomValue } from "jotai";

import ResponsiveImage from "common/components/ResponsiveImage";
import AdminProfileMiniItem from "common/components/admin/profile/AdminProfileMiniItem";
import { selectedProfileAtom } from "common/state/selectedProfile";
import { adminActionLogs, updatePostAdmin } from "common/libraries/adminApi";
import { archivePost } from "common/libraries/api";
import { wrappedError } from "common/utils/errorUtils";

export interface Post {
  id: number;
  imageURL: string;
  nsfw: string;
  nsfwScore: number;
  nsfl: boolean;
  visibility: string;
  description: string;
  tags?: string;
  created_at: string;

  profile: {
    id: number;
    avatarURL: string | null;
    botID?: number | null;
    visibility?: string;
    nsfw: string;
    nsfl: boolean;
    name: string;
    username: string;
  };
}

interface AdminPostItemProps {
  post: Post;
  setPosts: Dispatch<React.SetStateAction<Post[]>>;
}

const AdminPostItem: React.FC<AdminPostItemProps> = ({ post, setPosts }) => {
  const selectedProfile = useAtomValue(selectedProfileAtom);

  const handleRemovePost = async (post_id: number, post_visibility: string) => {
    const confirmMessage = "Are you sure you want to delete this post?";
    if (!window.confirm(confirmMessage)) {
      console.debug(`Post deletion cancelled for ID: ${post_id}`);
      return;
    }

    try {
      // Update UI state immediately for better UX
      setPosts((prevPosts) => prevPosts.filter((post) => post.id !== post_id));

      // Make API call to archive the post
      const { data, error } = (await archivePost(post_id)).data;
      if (error) {
        throw wrappedError(error, "Error deleting post");
      }

      // Log admin action based on visibility
      const logPayload =
        post_visibility !== "hidden"
          ? {
              target: "posts",
              action: "remove",
              info: [
                {
                  table: "posts",
                  id: post_id,
                  old: { visibility: post_visibility },
                  new: { visibility: "hidden" },
                },
              ],
            }
          : {
              target: "posts",
              action: "delete",
              info: [
                {
                  table: "posts",
                  id: post_id,
                },
              ],
            };

      await adminActionLogs(logPayload);
    } catch (error) {
      console.error("Post removal failed:", error);
      alert(`Failed to remove post: ${error}`);
      // Optionally: Revert UI state on error
      // setPosts((prevPosts) => [...prevPosts, removedPost]);
    }
  };

  const handleMarkasNSFW = async (post_id: number, post_nsfw: string) => {
    const isNSFW = post_nsfw === "nsfw";
    const newNSFWStatus = isNSFW ? "normal" : "nsfw";

    const confirmMessage = `Are you sure you want to mark this post as ${newNSFWStatus.toUpperCase()}?`;
    if (!window.confirm(confirmMessage)) {
      console.debug(`NSFW status change cancelled for post ID: ${post_id}`);
      return;
    }

    try {
      // Update UI state optimistically
      setPosts((prev: Post[]) =>
        prev.map((p: Post) =>
          p.id === post_id ? { ...p, nsfw: newNSFWStatus } : p,
        ),
      );

      // Make API call
      const { data, error } = (
        await updatePostAdmin({
          post_id,
          contents: { nsfw: newNSFWStatus },
        })
      ).data;

      if (error) {
        throw wrappedError(error, "Error in handleMarkasNSFW");
      }

      // Log admin action
      await adminActionLogs({
        target: "posts",
        action: isNSFW ? "unmark_as_nsfw" : "mark_as_nsfw",
        info: [
          {
            table: "posts",
            id: post_id,
            old: { nsfw: post_nsfw },
            new: { nsfw: newNSFWStatus },
          },
        ],
      });

      console.debug(`Post ${post_id} NSFW status updated to: ${newNSFWStatus}`);
    } catch (err) {
      console.error("Failed to update NSFW status:", err);
      alert(`Failed to update NSFW status: ${err}`);

      // Revert UI state on error
      setPosts((prev: Post[]) =>
        prev.map((p: Post) =>
          p.id === post_id ? { ...p, nsfw: post_nsfw } : p,
        ),
      );
    }
  };

  const handleMarkasNSFL = async (post_id: number, post_nsfl: boolean) => {
    const newNSFLStatus = !post_nsfl;
    const newVisibility = newNSFLStatus ? "hidden" : "public";

    const confirmMessage = `Are you sure you want to mark this post as ${
      newNSFLStatus ? "NSFL" : "Normal"
    }?`;

    if (!window.confirm(confirmMessage)) {
      console.debug(`NSFL status change cancelled for post ID: ${post_id}`);
      return;
    }

    try {
      // Update UI state optimistically
      setPosts((prev: Post[]) =>
        prev.map((p: Post) =>
          p.id === post_id
            ? { ...p, nsfl: newNSFLStatus, visibility: newVisibility }
            : p,
        ),
      );

      // Make API call
      const { data, error } = (
        await updatePostAdmin({
          post_id,
          contents: {
            nsfl: newNSFLStatus,
            visibility: newVisibility,
          },
        })
      ).data;

      if (error) {
        throw wrappedError(error, "Error in handleMarkasNSFL");
      }

      // Log admin action
      await adminActionLogs({
        target: "posts",
        action: newNSFLStatus ? "mark_as_nsfl" : "unmark_as_nsfl",
        info: [
          {
            table: "posts",
            id: post_id,
            old: { nsfl: post_nsfl },
            new: { nsfl: newNSFLStatus },
          },
        ],
      });

      console.debug(`Post ${post_id} NSFL status updated to: ${newNSFLStatus}`);
    } catch (err) {
      console.error("Failed to update NSFL status:", err);
      alert(`Failed to update NSFL status: ${err}`);

      // Revert UI state on error
      setPosts((prev: Post[]) =>
        prev.map((p: Post) =>
          p.id === post_id
            ? {
                ...p,
                nsfl: post_nsfl,
                visibility: post_nsfl ? "hidden" : "public",
              }
            : p,
        ),
      );
    }
  };

  return (
    <div className="focus:shadow-outline relative flex w-full flex-col overflow-hidden rounded-lg border border-gray-200 text-base shadow-sm hover:shadow-lg focus-visible:ring focus-visible:ring-gray-500/75 dark:border-gray-500 dark:bg-slate-900 dark:text-white xl:flex-row">
      <Link
        href={post.imageURL ?? "#"}
        target="_blank"
        className="h-[200px] w-[160px] items-center overflow-hidden border-b border-r bg-blue-50 object-cover dark:bg-slate-800"
      >
        {post.imageURL && (
          <ResponsiveImage
            src={post.imageURL}
            alt="Post"
            size={[160, 200]}
            autoHeight
            cover
          />
        )}
      </Link>

      <div className="flex h-auto flex-1 flex-col justify-between gap-2 p-2">
        <div className="flex justify-between gap-8">
          <div className="space-y-0.5">
            {/* Post ID */}
            <div className="flex items-center gap-2">
              <div className="flex gap-1">
                <div>Post ID:</div>
                <Link
                  className="link"
                  href="/admin/posts/[id]/edit"
                  as={`/admin/posts/${post.id}/edit`}
                >
                  {post.id}
                </Link>
              </div>

              {/* NSFW */}
              {post.nsfw === "nsfw" && (
                <div className="flex items-center overflow-hidden rounded-full border border-red-400">
                  <p className="rounded-l-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-gray-700 dark:text-red-400">
                    NSFW
                  </p>

                  {!!post.nsfwScore && (
                    <p className="rounded-xl bg-white px-2.5 py-0.5 text-xs font-medium text-[#ff4747] dark:bg-gray-700 dark:text-gray-600">
                      {post.nsfwScore.toFixed(2)}
                    </p>
                  )}
                </div>
              )}

              {/* NSFL */}
              {post.nsfl && (
                <div className="rounded-xl border border-[#ff4747] bg-[#fcff3a] px-2.5 py-0.5 text-xs font-medium text-[#ff4747] dark:bg-gray-700 dark:text-red-500">
                  NSFL
                </div>
              )}

              {post.visibility !== "public" && (
                <div
                  className={
                    post.visibility === "public"
                      ? "rounded-xl border border-green-800 bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-300"
                      : post.visibility === "private"
                        ? "rounded-xl border border-pink-800 bg-pink-100 px-2.5 py-0.5 text-xs font-medium text-pink-800 dark:bg-pink-900 dark:text-pink-300"
                        : "rounded-xl border border-gray-800 bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                  }
                >
                  <div className="">
                    {post.visibility.charAt(0).toUpperCase() +
                      post.visibility.slice(1)}
                  </div>
                </div>
              )}
            </div>

            {/* Description */}
            <p>
              {"Description: "}
              <span className="text-gray-500">{post.description}</span>
            </p>
          </div>

          <div className="flex flex-col items-end gap-2">
            <AdminProfileMiniItem
              id={post.profile.id}
              avatarURL={post.profile.avatarURL}
              name={post.profile.name}
              username={post.profile.username}
              nsfw={post.profile.nsfw}
              nsfl={post.profile.nsfl}
              botID={post.profile.botID}
              visibility={post.profile.visibility}
              bgColor={"blue"}
            />
            <p>{dayjs(post.created_at).fromNow()}</p>
          </div>
        </div>

        <div className="flex w-full flex-row items-end justify-between gap-3 sm:flex-row">
          <div className="flex flex-col items-start gap-2 lg:flex-row">
            {post.tags?.split(",").map((tag: string, index: number) => (
              <div
                className="rounded bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-900 dark:text-gray-300"
                key={index}
              >
                {tag}
              </div>
            ))}
          </div>

          <div className="flex flex-col items-end justify-center gap-2 lg:flex-row">
            <div className="flex flex-col items-end gap-2 lg:flex-row">
              <button
                className={`flex w-fit items-center gap-1 rounded px-2 py-1 text-xs ${
                  post.nsfl == true
                    ? "bg-gray-600 text-white"
                    : "border border-red-600 bg-[#fcff3a] text-red-600"
                }`}
                onClick={() => {
                  handleMarkasNSFL(post.id, post.nsfl);
                }}
              >
                {post.nsfl ? <FiRotateCw /> : <FiAlertOctagon />}
                {post.nsfl == true ? "Unmark as NSFL" : "Mark as NSFL"}
              </button>
              <button
                className={`flex w-fit items-center gap-1 rounded ${
                  post.nsfw == "nsfw" ? "bg-green-600" : "bg-pink-600"
                } px-2 py-1 text-xs text-white`}
                onClick={() => {
                  handleMarkasNSFW(post.id, post.nsfw);
                }}
              >
                <FiFlag />
                {post.nsfw == "nsfw" ? "Unmark as NSFW" : "Mark as NSFW"}
              </button>
            </div>
            {selectedProfile?.users?.role == "admin" && (
              <Link href={`/admin/posts/${post.id}/edit`}>
                <button className="flex items-center gap-1 rounded bg-blue-600 px-2 py-1 text-xs text-white">
                  <FiEdit /> Edit
                </button>
              </Link>
            )}

            {post?.visibility == "hidden" &&
              selectedProfile?.users?.role != "moderator" && (
                <button
                  onClick={() => handleRemovePost(post?.id, post?.visibility)}
                  className={`flex items-center gap-1 rounded bg-red-600 px-2 py-1 text-xs text-white ${
                    post?.visibility == "hidden" && "bg-zinc-800"
                  }`}
                >
                  {post?.visibility == "hidden" ? (
                    <>
                      <FiDelete />
                      Delete
                    </>
                  ) : (
                    <>
                      <FiTrash2 />
                      Remove
                    </>
                  )}
                </button>
              )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminPostItem;
