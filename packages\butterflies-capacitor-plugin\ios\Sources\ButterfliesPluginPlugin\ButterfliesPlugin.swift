import Foundation
import Shake
import Capacitor

public class ButterfliesPlugin: NSObject {

    weak var capPlugin: CAPPlugin?

    public func onLoad(capPlugin: CAPPlugin) {
        self.capPlugin = capPlugin

        Shake.configuration.shakeOpenListener = { [weak self] in
            let data: [String: Any] = [:]
            self?.capPlugin?.notifyListeners("shakeOpenListener", data: data, retainUntilConsumed: true)
        }

        Shake.configuration.shakeSubmitListener = { [weak self] type, fields in
            let data = [
                "type": type,
                "fields": fields,
            ]
            self?.capPlugin?.notifyListeners("shakeSubmitListener", data: data, retainUntilConsumed: true)
        }

        Shake.configuration.shakeDismissListener = { [weak self] in
            let data: [String: Any] = [:]
            self?.capPlugin?.notifyListeners("shakeDismissListener", data: data, retainUntilConsumed: true)
        }
    }

    public func echo(_ value: String) -> String {
        print(value)
        return value
    }

    public func shakeRegisterUser(userId: String) {
        Shake.registerUser(userId: userId)
    }

    public func shakeUnregisterUser() {
        Shake.unregisterUser()
    }

    public func shakeUpdateUserMetadata(metadata: JSObject) {
        Shake.updateUserMetadata(metadata)
    }

    public func shakeSetMetadata(metadata: JSObject) {
        for (k, v) in metadata {
            let stringifiedValue = v as? String ?? String(describing: v)
            Shake.setMetadata(key: k, value: stringifiedValue)
        }
    }

    public func shakeClearMetadata() {
        Shake.clearMetadata()
    }

    public func shakeSilentReport(description: String?) {
        let reportConfiguration = ShakeReportConfiguration()
        Shake.silentReport(description: description, fileAttachBlock: { return [] }, reportConfiguration: reportConfiguration)
    }

    public func shakeShow() {
        Shake.show()
    }
}
