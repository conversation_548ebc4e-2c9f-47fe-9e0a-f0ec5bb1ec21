import { FiFlag } from "react-icons/fi";

import { updateProfile } from "common/libraries/api";
import { adminActionLogs } from "common/libraries/adminApi";

const AdminProfileAgeTypeButton = ({
  profile,
  profiles,
  setProfiles,
}: {
  profile: any;
  profiles: any;
  setProfiles: any;
}) => {
  const handleMarkUnderage = async (id: number, ageType: string) => {
    const newAgeType = ageType === "child" ? "adult" : "child";
    const confirmMessage = `Are you sure you want to mark this profile as ${newAgeType}?`;

    if (!confirm(confirmMessage)) return;

    try {
      // Update local state optimistically
      const updatedProfiles = profiles.map((p: any) =>
        p.id === id ? { ...p, ageType: newAgeType } : p,
      );
      setProfiles(updatedProfiles);

      // Update server
      const {
        data: { data: profileData, error: profileError },
      } = await updateProfile({
        updateContents: { age: newAgeType },
        profileId: id,
      });

      if (profileError) throw profileError;
    } catch (error) {
      // Revert on error
      setProfiles([...profiles]);
      alert(`Failed to mark as ${newAgeType}: ${error}`);
    } finally {
      // Log admin action
      await adminActionLogs({
        target: "profiles",
        action: ageType === "child" ? "mark_as_adult" : "mark_as_child",
        info: [
          {
            table: "profiles",
            id,
            old: { ageType },
            new: { ageType: newAgeType },
          },
        ],
      });
    }
  };

  return (
    <button
      className={`flex items-center gap-1 rounded border px-2 py-1 text-xs text-white ${
        profile?.ageType == "child"
          ? "border-orange-600 bg-orange-600"
          : "border-cyan-600 bg-cyan-600"
      }`}
      onClick={() => handleMarkUnderage(profile?.id, profile?.ageType)}
    >
      <FiFlag />
      {profile?.ageType == "child" ? "Mark Adult" : "Mark Child"}
    </button>
  );
};

export default AdminProfileAgeTypeButton;
