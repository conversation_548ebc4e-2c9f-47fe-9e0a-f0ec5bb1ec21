import Link from "common/utils/Link";
import ResponsiveImage from "common/components/ResponsiveImage";
import { DEFAULT_AVATAR_URL } from "common/config/constants";

interface AdminMiniProfileProps {
  id: number;
  avatarURL: string | null;
  visibility?: string;
  name: string;
  username: string;
  nsfw: string;
  nsfl: boolean;
  bgColor: string;
  botID?: number | null;
}

const AdminProfileMiniItem: React.FC<AdminMiniProfileProps> = ({
  id,
  avatarURL,
  visibility,
  name,
  username,
  nsfw,
  nsfl,
  bgColor,
  botID,
}) => {
  return (
    <div
      className={`${`border-${bgColor}-200 bg-${bgColor}-50`} flex h-fit w-fit min-w-[28rem] items-center gap-2 overflow-hidden rounded-l-[2.75rem] rounded-r-xl border p-1 pr-4`}
    >
      {/* Reportee Avatar */}
      <div className="min-w-10 h-20 w-20 flex-shrink-0 justify-center overflow-hidden rounded-full">
        <ResponsiveImage
          src={avatarURL || DEFAULT_AVATAR_URL}
          alt={"Reportee avatar"}
          size={[80, 80]}
        />
      </div>

      <div className="flex flex-col gap-1">
        {/* Reportee ID */}
        <div className="flex items-center gap-2">
          <div className="flex w-20 items-center gap-0.5">
            <p>ID:</p>
            <Link
              href="/admin/profiles/[id]"
              as={`/admin/profiles/${id}`}
              className="link"
            >
              {id}
            </Link>
          </div>

          {!!botID && !!visibility && (
            <div className="flex overflow-hidden rounded-full">
              {/* Type : Butterfly | Account */}
              <div
                className={`${
                  botID
                    ? "border-yellow-400 bg-yellow-100 text-yellow-800 dark:bg-gray-700 dark:text-yellow-400"
                    : "border-cyan-400 bg-cyan-100 text-cyan-800 dark:bg-gray-700 dark:text-cyan-400"
                } rounded-l-full border px-2.5 py-0.5 text-xs font-medium`}
              >
                {botID ? "Butterfly" : "Account"}
              </div>

              <div
                className={`rounded-r-full border px-2.5 py-0.5 text-xs font-medium ${
                  visibility === "public"
                    ? "border-green-400 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                    : visibility === "private"
                      ? "border-blue-400 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                      : "border-gray-400 bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                } `}
              >
                {visibility.charAt(0).toUpperCase() + visibility.slice(1)}
              </div>
            </div>
          )}

          {nsfw === "nsfw" && (
            <div className="rounded-xl border border-red-400 bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-gray-700 dark:text-red-400">
              NSFW
            </div>
          )}

          {nsfl == true && (
            <div className="rounded-xl border border-[#ff4747] bg-[#fcff3a] px-2.5 py-0.5 text-xs font-medium text-[#ff4747] dark:bg-gray-700 dark:text-red-500">
              NSFL
            </div>
          )}
        </div>

        {/* Reportee Name */}
        <div className="flex gap-1">
          <p>Name:</p>
          <p className="line-clamp-1 max-w-[48rem]">{name}</p>
        </div>

        {/* Reportee Username & NSFW & NSFL */}
        <div className="flex items-center gap-1">
          {/* Reportee Username */}
          <p>Username:</p>
          <Link href="/users/[id]" as={`/users/${username}`} className="link">
            {username}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default AdminProfileMiniItem;
