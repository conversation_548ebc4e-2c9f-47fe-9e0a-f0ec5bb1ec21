import { FiFlag, FiRotateCw } from "react-icons/fi";

import { updateProfile } from "common/libraries/api";
import { adminActionLogs } from "common/libraries/adminApi";

const AdminProfileNSFWButton = ({
  profile,
  profiles,
  setProfiles,
}: {
  profile: any;
  profiles: any;
  setProfiles: any;
}) => {
  const onMarkasNSFWClick = async (
    profileId: number,
    currentNsfwStatus: string,
  ) => {
    const isNsfw = currentNsfwStatus === "nsfw";
    const newNsfwStatus = isNsfw ? "normal" : "nsfw";

    const isConfirmed = confirm(
      `Are you sure you want to mark this profile as ${
        isNsfw ? "Normal" : "NSFW"
      }?`,
    );

    if (!isConfirmed) return;

    try {
      const updatedProfiles = profiles.map((p: any) =>
        p.id === profileId ? { ...p, nsfw: newNsfwStatus } : p,
      );
      setProfiles(updatedProfiles);

      const {
        data: { data: profileData, error: profileError },
      } = await updateProfile({
        updateContents: { nsfw: newNsfwStatus },
        profileId,
      });

      if (profileError) throw profileError;

      await adminActionLogs({
        target: "profiles",
        action: isNsfw ? "unmark_as_nsfw" : "mark_as_nsfw",
        info: [
          {
            table: "profiles",
            id: profileId,
            old: { nsfw: currentNsfwStatus },
            new: { nsfw: newNsfwStatus },
          },
        ],
      });
    } catch (error) {
      setProfiles([...profiles]);
      alert(`Failed to mark as NSFW: ${error}`);
    }
  };

  return (
    <button
      className={`flex items-center gap-1 rounded border px-2 py-1 text-xs ${
        profile?.nsfw == "nsfw"
          ? "border-green-600 bg-green-600 text-green-100"
          : "border-red-600 bg-red-600 text-red-100"
      }`}
      onClick={() => {
        onMarkasNSFWClick(profile?.id, profile?.nsfw);
      }}
    >
      {profile?.nsfw == "nsfw" ? <FiRotateCw /> : <FiFlag />}
      {profile?.nsfw == "nsfw" ? "Unmark NSFW" : "Mark NSFW"}
    </button>
  );
};

export default AdminProfileNSFWButton;
