package ai.butterflies.capacitorplugin

import com.getcapacitor.JSObject
import com.getcapacitor.Plugin
import com.getcapacitor.PluginCall
import com.getcapacitor.PluginMethod
import com.getcapacitor.annotation.CapacitorPlugin
import com.shakebugs.shake.Shake
import com.shakebugs.shake.report.ShakeDismissListener
import com.shakebugs.shake.report.ShakeOpenListener
import com.shakebugs.shake.report.ShakeSubmitListener

@CapacitorPlugin(name = "ButterfliesPlugin")
class ButterfliesPluginPlugin : Plugin() {

    private val implementation: ButterfliesPlugin = ButterfliesPlugin(capPlugin=this)

    init {
        Shake.getReportConfiguration().shakeOpenListener = object : ShakeOpenListener {
            override fun onShakeOpen() {
                val ret = JSObject()
                notifyListeners("shakeOpenListener", ret, true)
            }
        }

        Shake.getReportConfiguration().shakeSubmitListener = object : ShakeSubmitListener {
            override fun onShakeSubmit(reportType: String, fields: Map<String, String>) {
                val ret = JSObject()
                ret.put("type", reportType)

                val fieldsObj = JSObject()
                fields.forEach { (k, v) ->
                    fieldsObj.put(k, v)
                }
                ret.put("fields", fieldsObj)

                notifyListeners("shakeSubmitListener", ret, true)
            }
        }

        Shake.getReportConfiguration().shakeDismissListener = object : ShakeDismissListener {
            override fun onShakeDismiss() {
                val ret = JSObject()
                notifyListeners("shakeDismissListener", ret, true)
            }
        }
    }

    @PluginMethod
    fun echo(call: PluginCall) {
        val value = call.getString("value")

        val ret = JSObject()
        ret.put("value", implementation.echo(value!!))
        call.resolve(ret)
    }

    @PluginMethod
    fun shakeRegisterUser(call: PluginCall) {
        val userIdValue = call.getString("userId")
        if (userIdValue == null) {
            call.reject("missing userId")
            return
        }
        implementation.shakeRegisterUser(userId=userIdValue)
        call.resolve()
    }

    @PluginMethod
    fun shakeUnregisterUser(call: PluginCall) {
        implementation.shakeUnregisterUser()
        call.resolve()
    }

    @PluginMethod
    fun shakeUpdateUserMetadata(call: PluginCall) {
        val metadataValue = call.getObject("metadata")
        if (metadataValue == null) {
            call.reject("missing metadata")
            return
        }

        implementation.shakeUpdateUserMetadata(metadata=metadataValue)
        call.resolve()
    }

    @PluginMethod
    fun shakeSetMetadata(call: PluginCall) {
        val metadataValue = call.getObject("metadata")
        if (metadataValue == null) {
            call.reject("missing metadata")
            return
        }
        implementation.shakeSetMetadata(metadata=metadataValue)
        call.resolve()
    }

    @PluginMethod
    fun shakeClearMetadata(call: PluginCall) {
        implementation.shakeClearMetadata()
        call.resolve()
    }

    @PluginMethod
    fun shakeSilentReport(call: PluginCall) {
        val description = call.getString("description")
        implementation.shakeSilentReport(description=description)
        call.resolve()
    }

    @PluginMethod
    fun shakeShow(call: PluginCall) {
        implementation.shakeShow()
        call.resolve()
    }
}
