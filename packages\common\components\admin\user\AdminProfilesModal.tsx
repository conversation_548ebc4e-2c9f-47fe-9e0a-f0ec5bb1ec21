import { Dispatch } from "react";
import { FiX } from "react-icons/fi";
import Link from "next/link";

import ModalBox from "common/components/Modal/ModalBox";
import Loading from "common/components/Loading";
import { truncateName } from "common/components/Profile/SimilarProfileItem";
import ResponsiveImage from "common/components/ResponsiveImage";
import { DEFAULT_AVATAR_URL } from "common/config/constants";

interface ProfileProps {
  id: string;
  avatar_url: string;
  display_name: string;
  username: string;
}

interface ModalProps {
  username: string;
  isOpen: boolean;
  setIsOpen: Dispatch<boolean>;
  profiles: ProfileProps[];
  setProfiles: Dispatch<ProfileProps[]>;
}

const AdminProfilesModal = ({
  username,
  isOpen,
  setIsOpen,
  profiles,
  setProfiles,
}: ModalProps) => {
  const ModalContents = () => {
    return (
      <>
        <div className="flex min-w-[350px] content-between items-center justify-between rounded-t-2xl border-b border-gray-200 bg-darkgray p-3 dark:border-white-800 dark:text-white">
          <div
            className="absolute right-3 cursor-pointer text-black dark:text-white"
            onClick={() => {
              setIsOpen(false);
              setProfiles([]);
            }}
          >
            <FiX size={24} />
          </div>
          <h2 className="flex w-full justify-center text-xl font-semibold capitalize text-black dark:text-white">
            Profiles ({username})
          </h2>
          <div></div>
        </div>
        <div
          className="flex max-h-[50vh] flex-col overflow-y-auto border-2 border-none bg-darkgray p-2 text-black dark:text-white"
          onDragOver={(e) => e.preventDefault()}
        >
          {profiles.length > 0 ? (
            profiles?.map((profile: ProfileProps) => (
              <div
                className="w-full rounded-md px-2 hover:bg-slate-200"
                key={profile?.id}
              >
                <div className="flex items-center justify-between py-2">
                  <Link
                    className="w-full"
                    href={`/admin/profiles/${profile.id}/edit`}
                  >
                    <div className="flex w-full items-center">
                      <ResponsiveImage
                        src={profile.avatar_url || DEFAULT_AVATAR_URL}
                        alt="Notification image"
                        size={[48, 48]}
                        rounded
                        cover
                      />

                      <div className="ml-3 grow break-all">
                        <div className="line-clamp-1 font-semibold text-black-100 dark:text-white-100">
                          {truncateName({
                            name: profile?.display_name,
                            limit: 30,
                          })}
                        </div>
                        <div className="text-sm text-white-500">
                          @
                          {truncateName({ name: profile?.username, limit: 30 })}
                        </div>
                      </div>
                    </div>
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <div className="flex min-h-[48vh] items-center justify-center">
              <Loading size={7} />
            </div>
          )}
        </div>
      </>
    );
  };

  return (
    <ModalBox
      isOpen={isOpen}
      setIsOpen={setIsOpen}
      ModalContents={ModalContents}
      maxWidth={500}
    />
  );
};

export default AdminProfilesModal;
