"use client";
import React, { useContext, useEffect } from "react";
import { useHistory } from "react-router-dom";

import supabase from "common/libraries/supabaseClient";
import { useNextRouting } from "common/contexts/NextRoutingContext";
import { isCapacitor } from "common/utils/Helper";
import { useFeatureFlagEnabled } from "common/utils/posthog";
import { captureError } from "common/utils/sentryWrapper";
import { trackEvent } from "common/utils/trackEvent";
import { UserContext } from "common/contexts/UserContext";

export const revalidate = 0;

const Callback = () => {
  let router: any;

  const isWaitlistEnabled = useFeatureFlagEnabled("WAITLIST_ENABLED");

  if (isCapacitor()) {
    router = useHistory();
  } else {
    router = useNextRouting();
  }

  const { isAuthWaitingForNavigation, loadUser } = useContext(UserContext);

  const fetchSession = async () => {
    const { data: sessionData, error } = await supabase.auth.getSession();
    if (error) {
      captureError(error, "auth callback - getSession failed");
    }
    trackEvent("auth.provider.completed", {
      provider: isAuthWaitingForNavigation || "unknown",
      nativePlugin: false,
    });
    if (sessionData?.session?.access_token) {
      window.location.href = `butterflies://callback?access_token=${sessionData?.session?.access_token}&refresh_token=${sessionData?.session?.refresh_token}`;
    }
  };

  const loadSession = async () => {
    await fetchSession();
    await loadUser();
    router.replace("/");
  };

  useEffect(() => {
    loadSession();
  }, []);

  useEffect(() => {
    if (isCapacitor()) return;

    if (isWaitlistEnabled) {
      router?.prefetch("/waitlist");
    } else {
      router?.prefetch("/account_setup/start_birthday");
    }
  }, [isWaitlistEnabled, router]);

  return <></>;
};

export default Callback;
