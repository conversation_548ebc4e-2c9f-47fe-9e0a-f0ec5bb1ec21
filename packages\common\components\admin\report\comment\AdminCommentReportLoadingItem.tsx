const AdminCommentReportLoadingItem = () => {
  return (
    <div className="mx-auto flex w-full flex-col justify-start gap-4 bg-white text-xs dark:bg-black-100">
      <div className="focus:shadow-outline relative mt-3 w-full space-y-4 overflow-hidden rounded-lg border border-gray-200 bg-white text-left text-base font-medium text-gray-900 shadow-sm hover:shadow-lg focus-visible:ring focus-visible:ring-gray-500/75 dark:border-gray-500 dark:bg-slate-900 dark:text-white">
        <div className="relative flex w-full justify-between">
          <div className="flex w-full max-w-full flex-col lg:flex-row">
            <div className="flex w-full border-r sm:w-1/2">
              <div className="min-w-20 relative h-72 w-56 flex-shrink-0 animate-pulse border-r bg-gray-200" />

              <div className="relative flex flex-1 flex-col gap-2.5 p-4">
                <div className="relative flex h-fit w-fit min-w-[20rem] items-center gap-2 overflow-hidden rounded-l-[2.75rem] rounded-r-xl border border-red-200 bg-red-50 p-1 pr-4">
                  <div className="min-w-10 h-20 w-20 flex-shrink-0 animate-pulse justify-center overflow-hidden rounded-full bg-gray-300" />

                  <div className="flex flex-col gap-2">
                    <div className="flex h-4 w-16 animate-pulse gap-1 rounded-full bg-gray-300" />
                    <div className="flex h-4 w-32 animate-pulse gap-1 rounded-full bg-gray-300" />
                    <div className="flex h-4 w-32 animate-pulse gap-1 rounded-full bg-gray-300" />
                  </div>
                </div>

                <div className="h-4 w-full animate-pulse rounded-full bg-gray-200" />
                <div className="h-4 w-full animate-pulse rounded-full bg-gray-200" />
                <div className="h-4 w-full animate-pulse rounded-full bg-gray-200" />
                <div className="h-4 w-full animate-pulse rounded-full bg-gray-200" />

                <div className="absolute bottom-2 right-2 flex items-center justify-end gap-2">
                  <div className="h-6 w-28 animate-pulse rounded-full bg-gray-200" />
                  <div className="h-6 w-24 animate-pulse rounded-full bg-gray-200" />
                  <div className="h-6 w-24 animate-pulse rounded-full bg-gray-200" />
                </div>
              </div>
            </div>

            <div className="flex flex-col gap-2 p-4 pb-10 sm:w-1/2">
              <div className="relative flex h-fit w-fit min-w-[20rem] items-center gap-2 overflow-hidden rounded-l-[2.75rem] rounded-r-xl border border-blue-200 bg-blue-50 p-1 pr-4">
                <div className="min-w-10 h-20 w-20 flex-shrink-0 animate-pulse justify-center overflow-hidden rounded-full bg-gray-300" />

                <div className="flex flex-col gap-2">
                  <div className="flex h-4 w-16 animate-pulse gap-1 rounded-full bg-gray-300" />
                  <div className="flex h-4 w-32 animate-pulse gap-1 rounded-full bg-gray-300" />
                  <div className="flex h-4 w-32 animate-pulse gap-1 rounded-full bg-gray-300" />
                </div>
              </div>

              <div className="mb-2 h-5 w-2/3 animate-pulse rounded-md bg-gray-200" />

              <div className="flex flex-wrap gap-2">
                <div className="flex h-5 w-32 animate-pulse gap-1 rounded-full bg-gray-200" />
                <div className="flex h-5 w-32 animate-pulse gap-1 rounded-full bg-gray-200" />
                <div className="flex h-5 w-32 animate-pulse gap-1 rounded-full bg-gray-200" />
                <div className="flex h-5 w-64 animate-pulse gap-1 rounded-full bg-gray-200" />
                <div className="flex h-5 w-64 animate-pulse gap-1 rounded-full bg-gray-200" />
                <div className="flex h-5 w-64 animate-pulse gap-1 rounded-full bg-gray-200" />
                <div className="flex h-5 w-32 animate-pulse gap-1 rounded-full bg-gray-200" />
                <div className="flex h-5 w-64 animate-pulse gap-1 rounded-full bg-gray-200" />
              </div>
            </div>
          </div>

          <div className="absolute bottom-3 right-3 flex flex-wrap items-center justify-end gap-2">
            <div className="h-6 w-28 animate-pulse rounded-md bg-gray-200" />
            <div className="h-6 w-16 animate-pulse rounded-md bg-gray-200" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminCommentReportLoadingItem;
