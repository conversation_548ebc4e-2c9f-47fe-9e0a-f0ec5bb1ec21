"use client";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";

import { selectedProfileAtom } from "common/state/selectedProfile";
import { useAtomValue } from "jotai";
import { adminActionLogs, updatePostAdmin } from "common/libraries/adminApi";
import { wrappedError } from "common/utils/errorUtils";

interface PostType {
  id: string;
  slug: string;
  description: string;
  ai_caption: string;
  media_url: string;
  tags: string;
  prompt: string;
  visibility: string;
  created_at: string;
  nsfw: string;
}

interface AdminPostFormProps {
  post: PostType;
}

const AdminPostForm = ({ post }: AdminPostFormProps) => {
  const selectedProfile = useAtomValue(selectedProfileAtom);

  return (
    <Formik
      initialValues={{
        id: post.id || "",
        slug: post.slug || undefined,
        description: post.description || undefined,
        ai_caption: post.ai_caption || undefined,
        media_url: post.media_url || undefined,
        tags: post.tags || undefined,
        prompt: post.prompt || undefined,
        visibility: post.visibility || "draft",
        created_at: post.created_at || undefined,
        nsfw: post.nsfw || null,
      }}
      validationSchema={Yup.object({
        id: Yup.number().integer().required("Required"),
        slug: Yup.string().required("Required"),
        description: Yup.string().notRequired(),
        media_url: Yup.string().notRequired(),
        ai_caption: Yup.string().notRequired(),
        tags: Yup.string().notRequired(),
        prompt: Yup.string().notRequired(),
        visibility: Yup.string()
          .oneOf(
            ["draft", "public", "hidden", "archived"],
            "Invalid Visibility",
          )
          .required("Required"),
        nsfw: Yup.string()
          .oneOf(["normal", "nsfw"], "Invalid NSFW")
          .required("Required"),
      })}
      onSubmit={async (values, { setSubmitting }) => {
        let updatedValues = {
          slug: values.slug,
          description: values.description,
          ai_caption: values.ai_caption,
          media_url: values.media_url,
          tags: values.tags,
          prompt: values.prompt,
          visibility: values.visibility,
          nsfw: values.nsfw,
        };

        // If we're setting the post to public for the first time, change the time stamp
        if (values.visibility === "public" && post.visibility !== "public") {
          // updatedValues.created_at = new Date();
          updatedValues = {
            ...updatedValues,
            created_at: new Date(),
          };
        }

        try {
          const response = await updatePostAdmin({
            post_id: Number(values.id || 0),
            contents: updatedValues,
          });
          const { data, error } = response.data;

          if (error) {
            throw wrappedError(error, "Error updating post");
          }
        } catch (error) {
          alert("Failed to update post: " + error);
        } finally {
          await adminActionLogs({
            target: "posts",
            action: "edit",
            info: [
              {
                table: "posts",
                id: post?.id,
                old: {
                  slug: post.slug,
                  description: post.description,
                  ai_caption: post.ai_caption,
                  media_url: post.media_url,
                  tags: post.tags,
                  prompt: post.prompt,
                  visibility: post.visibility,
                  nsfw: post.nsfw,
                },
                new: updatedValues,
              },
            ],
          });
          setSubmitting(false);
        }
      }}
    >
      {() => (
        <Form className="space-y-4 rounded border bg-white p-4 dark:bg-slate-900">
          {/* ID Field */}
          {selectedProfile?.users?.role === "admin" && (
            <div className="flex flex-col space-y-2">
              <label
                htmlFor="id"
                className="text-lg font-medium text-black dark:text-white"
              >
                ID
              </label>
              <Field
                name="id"
                type="number"
                className="rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
                disabled
              />
              <ErrorMessage
                name="id"
                component="div"
                className="text-sm text-red-500"
              />
            </div>
          )}

          {/* Slug Field */}
          {selectedProfile?.users?.role === "admin" && (
            <div className="flex flex-col space-y-2">
              <label
                htmlFor="slug"
                className="text-lg font-medium text-black dark:text-white"
              >
                Slug
              </label>
              <Field
                name="slug"
                type="text"
                className="rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
                disabled={selectedProfile?.users?.role !== "admin"}
              />
              <ErrorMessage
                name="slug"
                component="div"
                className="text-sm text-red-500"
              />
            </div>
          )}

          {/* Description Field */}
          <div className="flex flex-col space-y-2">
            <label
              htmlFor="description"
              className="text-lg font-medium text-black dark:text-white"
            >
              Description
            </label>
            <Field
              as="textarea"
              name="description"
              type="text"
              className="rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
              disabled={selectedProfile?.users?.role !== "admin"}
            />
            <ErrorMessage
              name="description"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          {/* Media URL Field */}
          {selectedProfile?.users?.role === "admin" && (
            <div className="flex flex-col space-y-2">
              <label
                htmlFor="media_url"
                className="text-lg font-medium text-black dark:text-white"
              >
                Media URL
              </label>
              <Field
                as="textarea"
                name="media_url"
                type="text"
                className="rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
                disabled={selectedProfile?.users?.role !== "admin"}
              />
              <ErrorMessage
                name="media_url"
                component="div"
                className="text-sm text-red-500"
              />
            </div>
          )}

          {/* AI Image Caption Field */}
          <div className="flex flex-col space-y-2">
            <label
              htmlFor="ai_caption"
              className="text-lg font-medium text-black dark:text-white"
            >
              AI Image Caption
            </label>
            <Field
              as="textarea"
              name="ai_caption"
              type="text"
              className="rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
              disabled={selectedProfile?.users?.role !== "admin"}
              rows="8"
            />
            <ErrorMessage
              name="ai_caption"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          {/* Tags Field */}
          <div className="flex flex-col space-y-2">
            <label
              htmlFor="tags"
              className="text-lg font-medium text-black dark:text-white"
            >
              Tags
            </label>
            <Field
              name="tags"
              type="text"
              className="rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
              disabled={selectedProfile?.users?.role !== "admin"}
            />
            <ErrorMessage
              name="tags"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          {/* Prompt Field */}
          <div className="flex flex-col space-y-2">
            <label
              htmlFor="prompt"
              className="text-lg font-medium text-black dark:text-white"
            >
              Personality Promp
            </label>
            <Field
              as="textarea"
              name="prompt"
              className="rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
              rows="8"
              disabled={selectedProfile?.users?.role !== "admin"}
            />
            <ErrorMessage
              name="prompt"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          {/* Visibility Field */}
          <div className="flex flex-col space-y-2">
            <label
              htmlFor="visibility"
              className="text-lg font-medium text-black dark:text-white"
            >
              Visibility
            </label>
            <Field
              name="visibility"
              as="select"
              className="rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
              disabled={selectedProfile?.users?.role !== "admin"}
            >
              <option value="draft">Draft</option>
              <option value="public">Public</option>
              <option value="archived">Archived</option>
              <option value="hidden">Hidden</option>
            </Field>
            <ErrorMessage
              name="visibility"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label
              htmlFor="nsfw"
              className="text-lg font-medium text-black dark:text-white"
            >
              NSFW
            </label>
            <Field
              name="nsfw"
              as="select"
              className="rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:border-gray-500 focus:ring-gray-500 dark:border-gray-600 dark:bg-slate-900 dark:text-white dark:placeholder-gray-400 dark:focus:border-gray-500 dark:focus:ring-gray-500"
              disabled={selectedProfile?.users?.role !== "admin"}
            >
              <option value="normal">Normal</option>
              <option value="nsfw">NSFW</option>
            </Field>
            <ErrorMessage
              name="nsfw"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="mt-4 flex justify-center">
            <button
              type="submit"
              className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 disabled:cursor-not-allowed disabled:bg-blue-400"
              disabled={selectedProfile?.users?.role !== "admin"}
            >
              Update
            </button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default AdminPostForm;
