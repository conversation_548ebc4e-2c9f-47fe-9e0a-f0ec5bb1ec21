"use client";
import {
  SignInWithApple,
  SignInWithAppleOptions,
  SignInWithAppleResponse,
} from "@capacitor-community/apple-sign-in";
import { usePathname } from "next/navigation";
import { useContext, useMemo, useState } from "react";
import { BiLogoApple } from "react-icons/bi";

import { AUTH_EXCLUDED_URLS, MOBILE_SITE_URL, SITE_URL } from "common/config";
import { useTheme } from "common/contexts/ThemeContext";
import { UserContext } from "common/contexts/UserContext";
import supabase from "common/libraries/supabaseClient";
import { isCapacitor } from "common/utils/Helper";
import { useFeatureFlagEnabled } from "common/utils/posthog";
import CustomButtonV2 from "../../utils/CustomButtonV2";
import Loading from "../Loading";
import { useToast } from "common/contexts/ToastContext";
import { setItemStorage } from "common/utils/localStorageWrappers";
import { getRuntimeEnvironment } from "common/utils/RuntimeEnvironment";
import { useSetAtom } from "jotai";
import { nameFromAuthProviderAtom } from "common/state/userInfoState";
import { trackEvent } from "common/utils/trackEvent";
import { captureError } from "common/utils/sentryWrapper";

const redirectURL = isCapacitor()
  ? `${MOBILE_SITE_URL}/auth/callback`
  : `${SITE_URL}/auth/callback`;

const signInUsingSupabaseApple = async () => {
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: "apple",
    options: {
      redirectTo: redirectURL,
    },
  });

  if (error) {
    console.error(
      "Error signing in with Apple using supabase client:",
      error.message,
    );
    throw error;
  }

  return data;
};

const signInUsingCapacitorApple = async (setNameFromAuthProvider) => {
  let options: SignInWithAppleOptions = {
    clientId: "ai.butterflies.web",
    redirectURI: redirectURL,
    scopes: "email name",
  };

  let appleResponse: SignInWithAppleResponse;
  try {
    appleResponse = await SignInWithApple.authorize(options);
    // We choose to use just the given name instead of the full name,
    // because it's more natural for butterlies to use that when chatting
    // with the user.
    const name = appleResponse.response.givenName;
    setNameFromAuthProvider(name || undefined);
  } catch (error: any) {
    console.error(
      "Error signing in with Apple using capacitor plugin:",
      error.message,
    );
    console.error("error:", error);
    throw error;
  }

  const { data, error } = await supabase.auth.signInWithIdToken({
    provider: "apple",
    token: appleResponse.response.identityToken,
  });

  if (error) {
    console.error(
      "Error passing Apple auth code to supabase client:",
      error.message,
    );
    throw error;
  }

  return data;
};

export default function AppleSignupButton({
  signup = false,
  isNewStyle = false,
}) {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const forceDisableNativeAppleSignin = useFeatureFlagEnabled(
    "FORCE_DISABLE_NATIVE_APPLE_SIGNIN",
  );

  const {
    loadUser,
    loadUserData,
    isAuthWaitingForNavigation,
    setIsAuthWaitingForNavigation,
  } = useContext(UserContext);

  const setNameFromAuthProvider = useSetAtom(nameFromAuthProviderAtom);
  const { theme } = useTheme();
  const pathname = usePathname();

  const showLoading = isLoading || isAuthWaitingForNavigation === "apple";

  const isAuthExcluded = useMemo(
    () => AUTH_EXCLUDED_URLS.some((uri) => new RegExp(uri).test(pathname)),
    [pathname],
  );

  const { invokeToast } = useToast();

  const handleSignup = async () => {
    trackEvent("auth.provider.button_pressed", {
      provider: "apple",
    });

    setIsLoading(true);
    if (!isAuthExcluded) {
      setItemStorage("returnUrl", pathname);
    }

    let canUseNativePlugin;

    try {
      let data:
        | Awaited<ReturnType<typeof signInUsingSupabaseApple>>
        | Awaited<ReturnType<typeof signInUsingCapacitorApple>>;

      const platform = getRuntimeEnvironment()?.getPlatform();
      const isNative = getRuntimeEnvironment()?.isNative() === true;

      canUseNativePlugin =
        platform === "ios" && isNative && !forceDisableNativeAppleSignin;

      trackEvent("auth.provider.started", {
        provider: "apple",
        nativePlugin: canUseNativePlugin,
      });

      if (canUseNativePlugin) {
        data = await signInUsingCapacitorApple(setNameFromAuthProvider);
        if (data) {
          ///////////////////////////////
          // TODO: we have a few places where we do something like this.
          // TODO: need a better way to orchestrate post-auth logic and navigation
          setIsAuthWaitingForNavigation("apple");
          loadUser();
          loadUserData(data?.user);
          ///////////////////////////////
        }
        trackEvent("auth.provider.completed", {
          provider: "apple",
          nativePlugin: canUseNativePlugin,
        });
      } else {
        data = await signInUsingSupabaseApple();
      }

      console.log("User signed in with Apple:", data);
    } catch (error: any) {
      trackEvent("auth.provider.failed", {
        provider: "apple",
        nativePlugin: canUseNativePlugin,
        error,
      });

      if (typeof error.message !== "undefined") {
        if (
          typeof error.message === "string" &&
          error.message.includes("1001")
        ) {
          // user cancelled
        } else {
          invokeToast({ type: "error", text: error.message, position: "top" });
          captureError(error, "Failed to signup with Apple");
        }
      }
      console.error("Error signing in with Apple:", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {isNewStyle ? (
        <CustomButtonV2
          onClick={handleSignup}
          type="primary"
          disabled={showLoading}
        >
          <BiLogoApple size={24} className="absolute left-0 mr-2" />
          {signup ? "Sign up" : "Sign in"} with Apple
          {showLoading && (
            <Loading
              size={3}
              style={{
                marginLeft: "6px",
                color: theme === "dark" ? "black" : "white",
              }}
            />
          )}
        </CustomButtonV2>
      ) : (
        <button
          onClick={handleSignup}
          type="button"
          className="group relative flex w-full items-center justify-center rounded-md border border-transparent bg-black-100 px-4 py-3 text-sm font-medium text-white dark:bg-white-100 dark:text-black-100"
          disabled={showLoading}
        >
          <BiLogoApple size={22} className="mr-2" />
          {signup ? "Sign up" : "Sign in"} with Apple
          {showLoading && (
            <Loading
              size={3}
              style={{
                marginLeft: "6px",
                color: theme === "dark" ? "black" : "white",
              }}
            />
          )}
        </button>
      )}
    </>
  );
}
