import { ChangeEvent, Dispatch } from "react";
import { useAtomValue } from "jotai";

import CustomInput from "common/elements/CustomInput";
import { selectedProfileAtom } from "common/state/selectedProfile";

interface EmailModalContentProps {
  loading: boolean;
  isExist: boolean;
  isValid: boolean;
  handleChange: (e: ChangeEvent<HTMLInputElement>) => void;
  setIsOpenEmailModalContent: Dispatch<boolean>;
  email: string;
}

const EmailModalContent = ({
  loading,
  isExist,
  isValid,
  handleChange,
  setIsOpenEmailModalContent,
  email,
}: EmailModalContentProps) => {
  const selectedProfile = useAtomValue(selectedProfileAtom);

  const handleOk = () => {
    if (!email?.length || isExist || !isValid) return;
    setIsOpenEmailModalContent(false);
  };

  return (
    <>
      <div className="flex h-full flex-col justify-between">
        <div className="mx-auto flex w-full flex-col items-center justify-center px-12">
          <div className="flex flex-col">
            <div className="txt-ai-creation-title mt-10">Email</div>
            <div className="txt-ai-creation-subtitle mt-2">
              Choose a unique email that is verified
            </div>
          </div>
        </div>
        <div className="flex w-full flex-col px-6">
          {isExist && email && selectedProfile?.email !== email && (
            <p className="txt-profile-name-unavailable mb-9 self-center">
              Oops, email is already in use
            </p>
          )}
          {email && !isExist && isValid && selectedProfile?.email !== email && (
            <p className="txt-profile-name-available mb-9 self-center">
              Email is available
            </p>
          )}
          <CustomInput
            autoCapitalize="words"
            autoCorrect="none"
            autoFocus={true}
            keepFocused={true}
            placeholder="Email"
            onChange={handleChange}
            handleOk={handleOk}
            btnDisabled={!email?.length || isExist || !isValid}
            path=""
            loading={null}
            spinLoading={loading}
            value={email}
            maxLength={50}
          />
        </div>
      </div>
    </>
  );
};

export default EmailModalContent;
