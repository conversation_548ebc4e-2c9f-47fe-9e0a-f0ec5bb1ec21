"use client";
import { useState, useEffect, useContext, useCallback } from "react";
import {
  useRouter,
  useSearchParams,
  usePathname,
  redirect,
} from "next/navigation";

import { UserContext } from "common/contexts/UserContext";
import LoadingScreen from "common/components/LoadingScreen";
import SafeSessionStorage from "common/utils/SafeSessionStorage";
import { useFeatureFlagEnabled } from "common/utils/posthog";
import Waitlist from "common/components/Waitlist/Waitlist";
import { findUserInWaitlist } from "common/libraries/api";
import { isCapacitor, publicPaths } from "common/utils/Helper";
import { LANDING_ENABLED } from "common/config/experimental";
import { getItemStorage } from "common/utils/localStorageWrappers";

const AuthProvider = ({ children }) => {
  const {
    user,
    setIsAuthPage,
    userData,
    isWaitlisted,
    setIsWaitlisted,
    selectedProfile,
    isOnboarding,
  } = useContext(UserContext);
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(true);

  const isWaitlistEnabled = useFeatureFlagEnabled("WAITLIST_ENABLED");

  let isFilledBirth = true;

  const privatePaths = [
    "/admin",
    "/bots",
    "/me",
    "/messages",
    "/notifications",
    "/search",
    "/invitation/accepted",
    "/pricing",
    "/subscription_success",
    "/subscription_failure",
    "/bookmarks",
    "/story_tester",
  ];

  const authPaths = [
    "/users/signup/email",
    "/users/signin/email",
    "/users/resetpassword",
  ];

  const specialPaths = ["/users/profiles", "/waitlist"];

  const checkWaitlistStatus = useCallback(async () => {
    const { data: waitlistStatus } = await findUserInWaitlist({
      userId: user?.id,
    });

    setIsWaitlisted(waitlistStatus.data);
  }, [setIsWaitlisted, user?.id]);

  useEffect(() => {
    if (user && isWaitlistEnabled) checkWaitlistStatus();
  }, [checkWaitlistStatus, isWaitlistEnabled, user]);

  // Admin page routing based on roles
  useEffect(() => {
    if (window.location.href.includes("localhost:")) {
      return;
    }

    if (!userData || !pathname || !pathname.startsWith("/admin")) return;

    if (
      userData?.role === "moderator" &&
      !(
        [
          "/admin",
          "/admin/profiles",
          "/admin/reviews",
          "/admin/prompts",
        ].includes(pathname) ||
        pathname.startsWith("/admin/profiles") ||
        pathname.startsWith("/admin/reviews") ||
        pathname.startsWith("/admin/prompts") ||
        pathname.startsWith("/admin/posts/")
      )
    ) {
      router.push("/admin");
    }

    if (userData?.role === "user") {
      router.push("/error/404");
    }
  }, [pathname, userData]);

  useEffect(() => {
    if (user) {
      if (selectedProfile) {
        if (selectedProfile?.users?.birthday) {
          isFilledBirth = true;
        } else {
          isFilledBirth = false;
        }
      }
      authCheck(true);
    } else if (user === null) {
      authCheck(false);
    }
  }, [pathname, searchParams, user, userData, isWaitlisted, selectedProfile]);

  const authCheck = (isAuth: boolean) => {
    const redirectURLs = ["/users/signin"];
    const returnUrl = getItemStorage("returnUrl");
    const isSpecialURL = specialPaths.some((specialPath) =>
      pathname.startsWith(specialPath),
    );
    const isPublicURL = publicPaths.some((publicPath) =>
      pathname.startsWith(publicPath),
    );
    const isPrivateURL =
      privatePaths.some((privatePath) => pathname.startsWith(privatePath)) ||
      pathname === "/";

    const isAuthURL = authPaths.some((authPath) =>
      pathname.startsWith(authPath),
    );

    if (isAuth) {
      if (isAuthURL) {
        redirect("/");
      }

      const shouldPassUrl = publicPaths.some(
        (publicPath) =>
          publicPath !== "/users" &&
          publicPath !== "/auth" &&
          pathname.startsWith(publicPath),
      );

      if (shouldPassUrl) {
      } else if (isWaitlisted && isWaitlistEnabled) {
        router.push("/waitlist");
      } else if (!isFilledBirth) {
        router.push("/account_setup/start_birthday");
      } else if (isOnboarding && !pathname?.startsWith("/account_setup")) {
        router.push("/onboarding");
      } else if (
        userData &&
        returnUrl &&
        returnUrl != "undefined" &&
        returnUrl != "null"
      ) {
        SafeSessionStorage.removeItem("returnUrl");
        router.push(returnUrl);
      } else if (redirectURLs.includes(pathname)) {
        router.push("/");
        setIsAuthPage(null);
      }
    } else if (isPrivateURL || isSpecialURL) {
      const url = pathname + (searchParams ? `?${searchParams}` : "");

      if (url === "/" || url.startsWith("/?")) {
        if (LANDING_ENABLED && !isCapacitor()) {
          redirect("/landing");
        } else {
          redirect("/users/signin");
        }
      } else router.push(`/users/signin?returnUrl=${url}`);
    } else if (!isPublicURL && pathname !== "/") {
      router.push("/");
    }

    setIsLoading(false);
  };

  return isLoading ? (
    <LoadingScreen />
  ) : user && isWaitlisted && isWaitlistEnabled ? (
    <Waitlist />
  ) : (
    children
  );
};

export default AuthProvider;
