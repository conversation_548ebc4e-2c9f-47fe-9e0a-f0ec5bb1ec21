"use client";
import React, { Dispatch } from "react";
import { FiExternalLink } from "react-icons/fi";
import { useAtomValue } from "jotai";

import Link from "common/utils/Link";
import ResponsiveImage from "common/components/ResponsiveImage";
import { DEFAULT_AVATAR_URL } from "common/config/constants";
import { selectedProfileAtom } from "common/state/selectedProfile";
import AdminProfileReportDoneButton from "common/components/admin/report/profile/AdminProfileReportDoneButton";
import AdminProfileReportNSFLButton from "common/components/admin/report/profile/AdminProfileReportNSFLButton";
import AdminProfileReportNSFWButton from "common/components/admin/report/profile/AdminProfileReportNSFWButton";

/// Type of profile report
export interface AdminProfileReportType {
  id: number;

  reportee: {
    id: number;
    avatarUrl: string;
    name: string;
    username: string;
    description: string;
    nsfw: string;
    nsfl: boolean;
    visibility: string;
    botID: number | null;
    creatorID: string | null;
  };

  reporter: {
    id: number;
    avatarUrl: string;
    name: string;
    username: string;
  };

  providedReason: string;
  reasonType: string[];
}

const AdminProfileReportItem = ({
  report,
  setReports,
}: {
  report: AdminProfileReportType;
  setReports: Dispatch<React.SetStateAction<AdminProfileReportType[]>>;
}) => {
  const selectedProfile = useAtomValue(selectedProfileAtom);

  return (
    <div className="mt-3 w-full space-y-4 overflow-hidden rounded-lg rounded-l-full border border-gray-200 bg-white text-left text-base font-medium text-gray-900 shadow-sm hover:shadow-lg focus-visible:ring focus-visible:ring-gray-500/75 dark:border-gray-500 dark:bg-slate-900 dark:text-white">
      <div className="flex w-full flex-col divide-x lg:flex-row">
        <div className="flex w-full flex-row sm:w-2/3">
          {/* Reportee Profile */}
          <div className="flex w-full flex-row justify-between gap-2">
            {/* Reportee Avatar */}
            <div className="min-w-10 m-1 h-52 w-52 flex-shrink-0 justify-center overflow-hidden rounded-full">
              <ResponsiveImage
                src={report.reportee.avatarUrl || DEFAULT_AVATAR_URL}
                alt={"Reportee avatar"}
                size={[208, 208]}
              />
            </div>

            <div className="relative flex w-full flex-col gap-0.5 p-2">
              {/* Reportee ID */}
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <p>ID:</p>
                  <Link
                    href="/admin/profiles/[id]"
                    as={`/admin/profiles/${report.reportee.id}`}
                    className="link"
                  >
                    {report.reportee.id}
                  </Link>
                </div>

                <div className="flex overflow-hidden rounded-full">
                  {/* Type : Butterfly | Account */}
                  <div
                    className={`${
                      report.reportee.botID
                        ? "border-yellow-400 bg-yellow-100 text-yellow-800 dark:bg-gray-700 dark:text-yellow-400"
                        : "border-cyan-400 bg-cyan-100 text-cyan-800 dark:bg-gray-700 dark:text-cyan-400"
                    } rounded-l-full border px-2.5 py-0.5 text-sm font-medium`}
                  >
                    {report.reportee.botID ? "Butterfly" : "Account"}
                  </div>

                  <div
                    className={`rounded-r-full border px-2.5 py-0.5 text-sm font-medium ${
                      report.reportee.visibility === "public"
                        ? "border-green-400 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                        : report.reportee.visibility === "private"
                          ? "border-blue-400 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                          : "border-gray-400 bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                    } `}
                  >
                    {report.reportee.visibility?.charAt(0).toUpperCase() +
                      report.reportee.visibility?.slice(1)}
                  </div>
                </div>

                {/* Reportee Profile NSFW */}
                {report.reportee.nsfw === "nsfw" && (
                  <div className="ml-2 rounded-xl border border-red-400 bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-gray-700 dark:text-red-400">
                    NSFW
                  </div>
                )}

                {/* Reportee Profile NSFL */}
                {report.reportee.nsfl && (
                  <div className="rounded-xl border border-[#ff4747] bg-[#fcff3a] px-2.5 py-0.5 text-xs font-medium text-[#ff4747] dark:bg-gray-700 dark:text-red-500">
                    NSFL
                  </div>
                )}
              </div>

              {/* Reportee Name */}
              <div className="flex gap-1">
                <p>Name:</p>
                <p className="line-clamp-1 max-w-[48rem]">
                  {report.reportee.name}
                </p>
              </div>

              <div className="flex items-center gap-1">
                {/* Reportee Username */}
                <p>Username:</p>
                {report.reportee.username}
              </div>

              {/* Reportee Description */}
              <div className="overflow-y-auto">
                {"Description: "}
                <span className="text-gray-500">
                  {report.reportee.description}
                </span>
              </div>

              <div className="mt-auto flex w-full items-center justify-end gap-2">
                {/* Mark as NSFW */}
                <AdminProfileReportNSFWButton
                  reporteeID={report.reportee.id}
                  nsfw={report.reportee.nsfw}
                  setReports={setReports}
                />

                {/* Mark as NSFL */}
                <AdminProfileReportNSFLButton
                  reporteeID={report.reportee.id}
                  nsfl={report.reportee.nsfl}
                  setReports={setReports}
                />
              </div>

              {/* Reportee Creator */}
              <div className="absolute right-2 top-2 flex items-center gap-2">
                {selectedProfile?.users?.role == "admin" && (
                  // Visit Creator
                  <Link
                    target="_blank"
                    href="/users/[id]"
                    as={`/admin/users?query=${report.reportee.creatorID}&p=1`}
                    className="flex items-center gap-1 rounded-full border border-cyan-600 px-2 py-0.5 text-cyan-600 transition-colors hover:bg-cyan-600 hover:text-white dark:border-cyan-400 dark:bg-cyan-950 dark:text-cyan-400 dark:hover:border-cyan-600 dark:hover:bg-cyan-600 dark:hover:text-white"
                  >
                    <div className="text-sm font-bold">Creator</div>
                    <FiExternalLink />
                  </Link>
                )}

                <Link
                  target="_blank"
                  href="/users/[id]"
                  as={`/users/${report.reportee.username}`}
                  className="flex items-center gap-1 rounded-full border border-blue-600 px-2 py-0.5 text-blue-600 transition-colors hover:bg-blue-600 hover:text-white dark:border-blue-400 dark:bg-blue-950 dark:text-blue-400 dark:hover:border-blue-600 dark:hover:bg-blue-600 dark:hover:text-white"
                >
                  <div className="text-sm font-bold">Visit User</div>
                  <FiExternalLink />
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Reporter */}
        <div className="flex flex-col justify-between gap-2 p-3 sm:w-1/3">
          <div className="flex flex-col gap-2">
            {/* Reporter Profile */}
            <div className="relative flex h-fit w-fit min-w-[20rem] items-center gap-2 overflow-hidden rounded-l-[2.75rem] rounded-r-xl border border-blue-200 bg-blue-50 p-1 pr-4">
              {/* Reporter Avatar */}
              <div className="min-w-10 h-20 w-20 flex-shrink-0 justify-center overflow-hidden rounded-full">
                <ResponsiveImage
                  src={report.reporter.avatarUrl || DEFAULT_AVATAR_URL}
                  alt={"Reporter avatar"}
                  size={[80, 80]}
                />
              </div>

              <div className="flex flex-col gap-0.5">
                {/* Reporter ID */}
                <div className="flex gap-1">
                  <p>ID:</p>
                  <Link
                    href="/admin/profiles/[id]"
                    as={`/admin/profiles/${report.reporter.id}`}
                    className="link"
                  >
                    {report.reporter.id}
                  </Link>
                </div>

                {/* Reporter Name */}
                <div className="flex gap-1">
                  <p>Name:</p>
                  <p className="line-clamp-1 max-w-[48rem]">
                    {report.reporter.name}
                  </p>
                </div>

                {/* Reporter Username */}
                <div className="flex gap-1">
                  <p>Username:</p>
                  <Link
                    href="/users/[id]"
                    as={`/users/${report.reporter.username}`}
                    className="link"
                  >
                    {report.reporter.username}
                  </Link>
                </div>
              </div>
            </div>

            {/* Reason Comment */}
            <p className="w-fit rounded-md bg-red-200 px-3">
              {report.providedReason}
            </p>

            {/* Reason Types */}
            <div className="flex max-w-full flex-wrap gap-2">
              {report.reasonType.map((type: string, index: number) => {
                return (
                  <div
                    key={index}
                    className="w-fit rounded-full bg-gray-200 px-3"
                  >
                    {type}
                  </div>
                );
              })}
            </div>
          </div>

          <div className="flex w-full justify-end">
            {/* Done : Button */}
            <AdminProfileReportDoneButton
              reportID={report.id}
              setReports={setReports}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminProfileReportItem;
