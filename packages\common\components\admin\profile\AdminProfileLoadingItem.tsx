const AdminProfileLoadingItem = () => {
  return (
    <div className="focus:shadow-outline mt-3 flex w-full items-center justify-between overflow-hidden rounded-lg border border-gray-200 bg-white text-left text-sm font-medium text-gray-900 shadow-sm hover:shadow-lg focus-visible:ring focus-visible:ring-gray-500/75 dark:border-gray-500 dark:bg-slate-900 dark:text-white">
      <div className="flex h-full w-full">
        <div className="hidden h-[240px] w-[240px] items-center object-cover sm:block">
          <svg
            className="-ml-3 h-auto w-auto text-gray-200 dark:text-gray-600"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            viewBox="0 0 18 20"
          >
            <path d="M18 0H2a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2Zm-5.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm4.376 10.481A1 1 0 0 1 16 15H4a1 1 0 0 1-.895-1.447l3.5-7A1 1 0 0 1 7.468 6a.965.965 0 0 1 .9.5l2.775 4.757 1.546-1.887a1 1 0 0 1 1.618.1l2.541 4a1 1 0 0 1 .028 1.011Z" />
          </svg>
        </div>
        <div className="flex h-auto flex-1 flex-grow flex-col justify-between gap-2 p-2">
          <div className="relative flex w-full justify-start gap-3 sm:justify-between">
            <div className="flex h-[240px] w-[240px] items-center overflow-hidden rounded-lg object-cover sm:hidden">
              <svg
                className="h-auto w-auto text-gray-200 dark:text-gray-600"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M18 0H2a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2Zm-5.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm4.376 10.481A1 1 0 0 1 16 15H4a1 1 0 0 1-.895-1.447l3.5-7A1 1 0 0 1 7.468 6a.965.965 0 0 1 .9.5l2.775 4.757 1.546-1.887a1 1 0 0 1 1.618.1l2.541 4a1 1 0 0 1 .028 1.011Z" />
              </svg>
            </div>
            <div className="flex flex-col gap-2 sm:flex-row">
              <div className="flex gap-1">
                <div className="h-4 w-24 rounded-full bg-gray-200 dark:bg-gray-700"></div>
              </div>
              <div className="flex gap-1">
                <div className="h-4 w-24 rounded-full bg-gray-200 dark:bg-gray-700"></div>
              </div>
              <div className="flex gap-1">
                <div className="h-4 w-24 rounded-full bg-gray-200 dark:bg-gray-700"></div>
              </div>
            </div>
            <div className="absolute right-0.5 top-0.5 flex flex-col items-end gap-1 sm:flex-row">
              <div className="h-6 w-20 rounded-md bg-gray-200 dark:bg-gray-700"></div>
              <div className="h-6 w-20 rounded-md bg-gray-200 dark:bg-gray-700"></div>
              <div className="h-6 w-20 rounded-md bg-gray-200 dark:bg-gray-700"></div>

              <div className="h-6 w-20 rounded-md bg-gray-200 dark:bg-gray-700"></div>
            </div>
          </div>

          <div className="flex flex-wrap items-start gap-1 md:flex-row md:gap-3"></div>

          <div className="h-6 w-36 rounded-md bg-gray-200 dark:bg-gray-700"></div>

          <div className="h-3 w-[600px] rounded-full bg-gray-200 dark:bg-gray-700"></div>
          <div className="flex flex-row justify-between gap-2">
            <div className="flex flex-col justify-center gap-2 sm:flex-row">
              <div className="h-6 w-28 rounded-full bg-gray-200 dark:bg-gray-700"></div>
              <div className="h-6 w-28 rounded-full bg-gray-200 dark:bg-gray-700"></div>
              <div className="h-6 w-28 rounded-full bg-gray-200 dark:bg-gray-700"></div>
            </div>
            <div className="flex flex-col items-end justify-between gap-2 sm:flex-row">
              <div className="h-6 w-20 rounded-md bg-gray-200 dark:bg-gray-700"></div>
              <div className="h-6 w-20 rounded-md bg-gray-200 dark:bg-gray-700"></div>
              <div className="h-6 w-20 rounded-md bg-gray-200 dark:bg-gray-700"></div>
              <div className="h-6 w-20 rounded-md bg-gray-200 dark:bg-gray-700"></div>
              <div className="h-6 w-20 rounded-md bg-gray-200 dark:bg-gray-700"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminProfileLoadingItem;
