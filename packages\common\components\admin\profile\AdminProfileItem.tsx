"use client";
import React from "react";
import { FiExternalLink } from "react-icons/fi";
import Link from "next/link";
import dayjs from "dayjs";
import { useAtomValue } from "jotai";

import ResponsiveImage from "common/components/ResponsiveImage";
import AdminProfileInfoItem from "./AdminProfileInfoItem";
import {
  AdminProfileFollowsModalButton,
  AdminProfilePostsModalButton,
  AdminProfileNSFWButton,
  AdminProfileNSFLButton,
  AdminProfileRemoveButton,
} from "./buttons";
import { selectedProfileAtom } from "common/state/selectedProfile";
import AdminProfileUnderAgeButton from "common/components/admin/profile/buttons/AdminProfileAgeTypeButton";

export interface ProfileType {
  id: number;
  avatarURL: string;
  createdAt: Date;
  username: string;
  displayName: string;
  location: string;
  description: string;
  nsfw: string;
  nsfl: boolean;
  visibility: string;
  ageType: string | null;
  creatorID: string;
  botID: number | null;
  followerCount: number;
  followingCount: number;
  totalPostsCount: number;
  publicPostsCount: number;
}

const AdminProfileItem = ({
  profile,
  profiles,
  setProfiles,
}: {
  profile: ProfileType;
  profiles: any;
  setProfiles: any;
}) => {
  const selectedProfile = useAtomValue(selectedProfileAtom);

  return (
    <>
      <div className="focus:shadow-outline relative mt-3 flex w-full items-center justify-between overflow-hidden rounded-lg rounded-tl-[5rem] border border-gray-200 bg-white text-base font-medium text-gray-900 shadow-sm hover:shadow-lg focus-visible:ring focus-visible:ring-gray-500/75 dark:border-gray-500 dark:bg-slate-900 dark:text-gray-300 md:rounded-l-full md:rounded-r-xl">
        {/* Tags */}
        <div className="absolute right-2.5 top-2.5 flex flex-col items-end">
          <div className="flex flex-col items-center gap-2 md:flex-row">
            {/* Go to Profile */}
            {/* <Link
              target="_blank"
              href="/users/[id]"
              as={`/users/${profile.username}`}
              className="flex items-center gap-1 rounded-full border border-blue-600 px-2 py-0.5 text-blue-600 transition-colors hover:bg-blue-600 hover:text-white dark:border-blue-400 dark:bg-blue-950 dark:text-blue-400 dark:hover:border-blue-600 dark:hover:bg-blue-600 dark:hover:text-white"
              >
              <div className="text-sm font-bold">Go to Profile</div>
              <FiExternalLink />
            </Link> */}

            {selectedProfile?.users?.role == "admin" && (
              // Visit Creator
              <Link
                target="_blank"
                href="/users/[id]"
                as={`/admin/users?query=${profile.creatorID}&p=1`}
                className="flex items-center gap-1 rounded-full border border-blue-600 px-2 py-0.5 text-blue-600 transition-colors hover:bg-blue-600 hover:text-white dark:border-blue-400 dark:bg-blue-950 dark:text-blue-400 dark:hover:border-blue-600 dark:hover:bg-blue-600 dark:hover:text-white"
              >
                <div className="text-sm font-bold">Creator</div>
                <FiExternalLink />
              </Link>
            )}
          </div>

          {/* Created Date */}
          <div className="pr-1">{dayjs(profile.createdAt).fromNow()}</div>
        </div>

        <div className="flex w-full flex-col items-center lg:flex-row">
          {/* Avatar : Image */}
          <div className="m-2 h-40 w-40 shrink-0 overflow-hidden rounded-full border-r bg-blue-50 object-cover dark:bg-blue-950 md:m-1 md:h-60 md:w-60">
            {profile.avatarURL && (
              <Link href={profile.avatarURL}>
                <ResponsiveImage
                  src={profile.avatarURL}
                  alt="Profile"
                  size={[240, 240]}
                  autoHeight
                  cover
                />
              </Link>
            )}
          </div>

          <div className="flex flex-1 flex-col justify-between p-3">
            <AdminProfileInfoItem profile={profile} />

            <div className="flex justify-between gap-2 lg:flex-col 2xl:flex-row">
              {/* Followers & Followings & Posts */}
              <div className="flex flex-col items-start justify-end gap-4 lg:flex-row">
                {/* Followers & Followings */}
                <AdminProfileFollowsModalButton profile={profile} />

                {/* Posts(All / Public) */}
                <AdminProfilePostsModalButton profile={profile} />
              </div>

              <div className="flex flex-col items-end justify-end gap-2 lg:flex-row">
                {/* Mark as UnderAge */}
                <AdminProfileUnderAgeButton
                  profile={profile}
                  profiles={profiles}
                  setProfiles={setProfiles}
                />

                {/* Mark as NSFW */}
                <AdminProfileNSFWButton
                  profile={profile}
                  profiles={profiles}
                  setProfiles={setProfiles}
                />

                {/* Mark as NSFL */}
                <AdminProfileNSFLButton
                  profile={profile}
                  profiles={profiles}
                  setProfiles={setProfiles}
                />

                {selectedProfile?.users?.role == "admin" && (
                  <>
                    {/* {profile.botID && (
                      <Link href={`/admin/ais/${profile.botID}`}>
                        <button className="flex items-center gap-1 rounded bg-sky-600 px-2 py-1 text-xs text-white">
                          <FiLink /> Go to Bot
                        </button>
                      </Link>
                    )} */}

                    {/* Edit */}
                    {/* <Link href={`/admin/profiles/${profile.id}/edit`}>
                      <button className="flex items-center gap-1 rounded bg-blue-600 px-2 py-1 text-xs text-white">
                        <FiEdit /> Edit
                      </button>
                    </Link> */}

                    {/* Delete or Remove */}
                    <AdminProfileRemoveButton
                      profile={profile}
                      profiles={profiles}
                      setProfiles={setProfiles}
                    />
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AdminProfileItem;
