"use client";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";

import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { useRouter } from "next/navigation";
import { adminDeleteBot } from "common/libraries/adminApi";
import { updateBot } from "common/libraries/api";

dayjs.extend(utc);
dayjs.extend(timezone);

const BotStatusOptions = [
  { value: "online", label: "Online" },
  { value: "away", label: "Away" },
  { value: "offline", label: "Offline" },
  // Add more options as needed
];

const toPacificDateTime = (isoString) => {
  if (!isoString) return undefined;
  return dayjs
    .utc(isoString)
    .tz("America/Los_Angeles")
    .format("YYYY-MM-DDTHH:mm:ss");
};

const toUTCDateTime = (pacificDateTime) => {
  if (!pacificDateTime) return undefined;
  return dayjs
    .tz(pacificDateTime, "America/Los_Angeles")
    .utc()
    .format("YYYY-MM-DDTHH:mm:ss.SSS[Z]");
};

const BotForm = ({ bot }) => {
  const router = useRouter();
  const handleDeleteBot = async (e: any) => {
    e.preventDefault();

    if (confirm("Are you sure? this cannot be undone")) {
      try {
        await adminDeleteBot(bot.id);
        router.push("/admin/ais");
      } catch (err) {
        console.error("Failed to delete bot by admin", err);
      }
    }
  };
  return (
    <Formik
      initialValues={{
        id: bot.id || "",
        bio: bot.bio || undefined,
        description: bot.description || undefined,
        location: bot.location || undefined,
        seaart_token: bot.seaart_token || undefined,
        timezone: bot.timezone || undefined,
        last_start: toPacificDateTime(bot.last_start) || undefined,
        status: bot.status || "offline",
        active_hours_per_day: bot.active_hours_per_day || undefined,
        wake_up_interval: bot.wake_up_interval || undefined,
        wake_up_time: bot.wake_up_time || undefined,
      }}
      validationSchema={Yup.object({
        id: Yup.number().integer().required("Required"),
        bio: Yup.string().notRequired(),
        description: Yup.string().notRequired(),
        location: Yup.string().notRequired(),
        seaart_token: Yup.string().notRequired(),
        timezone: Yup.string().required("Required"),
        status: Yup.string()
          .oneOf(["online", "offline"], "Invalid Status")
          .required("Required"),
        active_hours_per_day: Yup.number()
          .integer()
          .min(0)
          .max(24)
          .required("Required"),
        wake_up_interval: Yup.number().integer().required("Required"),
        wake_up_time: Yup.number().required("Required"),
      })}
      onSubmit={async (values, { setSubmitting }) => {
        try {
          await updateBot(values?.id, {
            bio: values.bio,
            description: values.description,
            location: values.location,
            seaart_token: values.seaart_token,
            timezone: values.timezone,
            last_start: toUTCDateTime(values.last_start),
            status: values.status,
            active_hours_per_day: values.active_hours_per_day,
            wake_up_interval: values.wake_up_interval,
            wake_up_time: values.wake_up_time,
          });

          window.location.reload();
        } catch (error) {
          alert("Failed to update bot");
        } finally {
          setSubmitting(false);
        }
      }}
    >
      {() => (
        <Form
          className="space-y-4 rounded border bg-white p-4 text-black dark:border-slate-700 dark:bg-slate-800 dark:text-gray-200"
          noValidate
        >
          <div className="flex flex-col space-y-2">
            <label htmlFor="id" className="text-lg font-medium">
              ID
            </label>
            <Field
              name="id"
              type="number"
              className="rounded border bg-white p-2 dark:bg-slate-900"
              disabled
            />
            <ErrorMessage
              name="id"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label htmlFor="bio" className="text-lg font-medium">
              Bio
            </label>
            <Field
              name="bio"
              type="text"
              className="rounded border bg-white p-2 dark:bg-slate-900"
              component="textarea"
            />
            <ErrorMessage
              name="bio"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label htmlFor="description" className="text-lg font-medium">
              Description
            </label>
            <Field
              name="description"
              as="textarea"
              type="text"
              className="rounded border bg-white p-2 dark:bg-slate-900"
            />
            <ErrorMessage
              name="description"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label htmlFor="location" className="text-lg font-medium">
              Location
            </label>
            <Field
              name="location"
              type="text"
              className="rounded border bg-white p-2 dark:bg-slate-900"
            />
            <ErrorMessage
              name="location"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label htmlFor="seaart_token" className="text-lg font-medium">
              Seaart Token
            </label>
            <Field
              name="seaart_token"
              type="text"
              className="rounded border bg-white p-2 dark:bg-slate-900"
            />
            <ErrorMessage
              name="seaart_token"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label htmlFor="timezone" className="text-lg font-medium">
              Timezone
            </label>
            <Field
              name="timezone"
              type="text"
              className="rounded border bg-white p-2 dark:bg-slate-900"
            />
            <ErrorMessage
              name="timezone"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label htmlFor="last_start" className="text-lg font-medium">
              Last Start
            </label>
            <Field
              name="last_start"
              type="datetime-local"
              className="rounded border bg-white p-2 dark:bg-slate-900"
            />
            <ErrorMessage
              name="last_start"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label htmlFor="status" className="text-lg font-medium">
              Status
            </label>
            <Field
              name="status"
              as="select"
              className="rounded border bg-white p-2 dark:bg-slate-900"
            >
              <option value="online">Online</option>
              <option value="offline">Offline</option>
            </Field>
            <ErrorMessage
              name="status"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label
              htmlFor="active_hours_per_day"
              className="text-lg font-medium"
            >
              Active Hours Per Day
            </label>
            <Field
              name="active_hours_per_day"
              type="number"
              className="rounded border bg-white p-2 dark:bg-slate-900"
            />
            <ErrorMessage
              name="active_hours_per_day"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label htmlFor="wake_up_interval" className="text-lg font-medium">
              Wake Up Interval
            </label>
            <Field
              name="wake_up_interval"
              type="number"
              className="rounded border bg-white p-2 dark:bg-slate-900"
            />
            <ErrorMessage
              name="wake_up_interval"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label htmlFor="wake_up_time" className="text-lg font-medium">
              Wake Up Time
            </label>
            <Field
              name="wake_up_time"
              type="number"
              className="rounded border bg-white p-2 dark:bg-slate-900"
            />
            <ErrorMessage
              name="wake_up_time"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="mt-4 flex justify-center gap-4">
            <button
              type="submit"
              className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
            >
              Update
            </button>
            <button
              className="rounded bg-red-500 px-4 py-2 text-white hover:bg-red-600"
              onClick={handleDeleteBot}
            >
              Delete
            </button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default BotForm;
