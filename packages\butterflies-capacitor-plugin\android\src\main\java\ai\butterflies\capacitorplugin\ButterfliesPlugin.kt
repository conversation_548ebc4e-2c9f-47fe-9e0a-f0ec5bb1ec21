package ai.butterflies.capacitorplugin

import android.util.Log
import com.getcapacitor.JSObject
import com.getcapacitor.Plugin
import com.shakebugs.shake.Shake
import com.shakebugs.shake.ShakeReportConfiguration
import com.shakebugs.shake.report.ShakeDismissListener
import com.shakebugs.shake.report.ShakeOpenListener
import com.shakebugs.shake.report.ShakeSubmitListener

class ButterfliesPlugin(capPlugin: Plugin) {

    init {

    }

    fun echo(value: String): String {
        Log.i("Echo", value)
        return value
    }

    fun shakeRegisterUser(userId: String) {
        Shake.registerUser(userId)
    }

    fun shakeUnregisterUser() {
        Shake.unregisterUser()
    }

    fun shakeUpdateUserMetadata(metadata: JSObject) {
        val metadataMap = mutableMapOf<String, String>().apply {
            metadata.keys().forEach {
                val strVal = metadata[it] as? String ?: return@forEach
                put(it, strVal)
            }
        }
        Shake.updateUserMetadata(metadataMap)
    }

    fun shakeSetMetadata(metadata: JSObject) {
        metadata.keys().forEach {
            val strVal = metadata[it] as? String ?: return@forEach
            Shake.setMetadata(it, strVal)
        }
    }

    fun shakeClearMetadata() {
        Shake.clearMetadata()
    }

    fun shakeSilentReport(description: String?) {
        val reportConfiguration = ShakeReportConfiguration()
        Shake.silentReport(description, { emptyList() }, reportConfiguration)
    }

    fun shakeShow() {
        Shake.show()
    }
    
}
