"use client";
import { Formik, Form, Field, ErrorMessage } from "formik";
import { useRouter } from "next/navigation";
import * as Yup from "yup";

import {
  deleteProfile,
  updateConversationPrompt,
} from "common/libraries/adminApi";

const BotStatusOptions = [
  { value: "online", label: "Online" },
  { value: "away", label: "Away" },
  { value: "offline", label: "Offline" },
  // Add more options as needed
];

const PromptForm = ({ prompt }: { prompt: any }) => {
  const router = useRouter();

  const handleDeleteProfile = async (e: any) => {
    e.preventDefault();

    if (confirm("Are you sure? this cannot be undone")) {
      const deleteResult = await deleteProfile({ profileId: prompt?.id });
      const { data: profileData = null, error: profileError = null } =
        deleteResult.data;
      if (!profileError) {
        router.push("/admin/profiles");
      }
    }
  };

  return (
    <Formik
      initialValues={{
        name: prompt.name || "",
        prompt: prompt.prompt || "",
        frequency_penalty: prompt.frequency_penalty || 1.3,
        temperature: prompt.temperature || 0.7,
      }}
      validationSchema={Yup.object({
        name: Yup.string().notRequired(),
        prompt: Yup.string().notRequired(),
        frequency_penalty: Yup.number().min(0).required("Required"),
        temperature: Yup.number().min(0).required("Required"),
      })}
      onSubmit={async (values, { setSubmitting }) => {
        try {
          const { error } = (
            await updateConversationPrompt({
              id: prompt.id,
              prompt_content: {
                name: values.name,
                prompt: values.prompt,
                frequency_penalty: values.frequency_penalty,
                temperature: values.temperature,
              },
            })
          ).data;

          if (error) throw error;
          window.location.reload();
        } catch (error) {
          console.error("Failed to update prompt: ", error);
          alert("Failed to update prompt: " + error?.message);
        } finally {
          setSubmitting(false);
        }
      }}
    >
      {() => (
        <Form className="space-y-4 rounded border bg-white p-4">
          <div className="flex flex-col space-y-2">
            <label htmlFor="frequency_penalty" className="text-lg font-medium">
              Name (Help to remember)
            </label>
            <Field name="name" type="text" className="rounded border p-2" />
            <ErrorMessage
              name="name"
              component="div"
              className="text-sm text-red-500"
            />
          </div>
          <div className="flex flex-col space-y-2">
            <label htmlFor="description" className="text-lg font-medium">
              Prompt
            </label>
            <Field
              name="prompt"
              as="textarea"
              type="text"
              className="h-20 rounded border p-2"
            />
            <ErrorMessage
              name="prompt"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label htmlFor="frequency_penalty" className="text-lg font-medium">
              Frequency Penalty
            </label>
            <Field
              name="frequency_penalty"
              type="number"
              className="rounded border p-2"
            />
            <ErrorMessage
              name="frequency_penalty"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label htmlFor="temperature" className="text-lg font-medium">
              Temperature
            </label>
            <Field
              name="temperature"
              type="number"
              className="rounded border p-2"
            />
            <ErrorMessage
              name="temperature"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-wrap justify-center">
            <div className="mr-4 mt-4 flex justify-center gap-3">
              <button
                type="submit"
                className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
              >
                Update
              </button>
              {/* <button
                className="rounded bg-red-500 px-4 py-2 text-white hover:bg-red-600"
                onClick={handleDeleteProfile}
              >
                Delete prompt
              </button> */}
            </div>

            <div className="mt-4 flex justify-center"></div>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default PromptForm;
