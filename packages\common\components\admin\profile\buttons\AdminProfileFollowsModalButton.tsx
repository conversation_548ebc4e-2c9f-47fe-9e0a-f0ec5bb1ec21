import { useState } from "react";
import Link from "next/link";
import { FiX } from "react-icons/fi";

import { DEFAULT_AVATAR_URL } from "common/config/constants";
import { truncateName } from "common/components/Profile/SimilarProfileItem";
import ResponsiveImage from "common/components/ResponsiveImage";
import ModalBox from "common/components/Modal/ModalBox";
import Loading from "common/components/Loading";
import {
  fetchFollowingProfilesForAdmin,
  fetchFollowerProfilesForAdmin,
} from "common/libraries/adminApi";

interface ProfileType {
  id: number;
  displayName: string;
  followerCount: number;
  followingCount: number;
}

interface FollowProfileType {
  id: number;
  avatar_url: string;
  display_name: string;
  username: string;
}

const AdminProfileFollowsModalButton = ({
  profile,
}: {
  profile: ProfileType;
}) => {
  const [follows, setFollows] = useState<FollowProfileType[]>([]);
  const [modalVisibility, setModalVisibility] = useState(false);

  type FollowResponse = {
    data: {
      data: Array<{ profiles: FollowProfileType }>;
      error: any;
    };
  };

  const fetchProfiles = async (
    profileId: number,
    fetchFn: (params: { profileId: number }) => Promise<FollowResponse>,
  ): Promise<FollowProfileType[]> => {
    try {
      const { data: profiles, error } = (await fetchFn({ profileId })).data;
      if (error) throw error;
      return profiles.map(({ profiles }) => profiles);
    } catch (error) {
      console.error("Error fetching profiles:", error);
      return [];
    }
  };

  const handleFollowClick = async (
    profile: ProfileType,
    type: "followers" | "followings",
  ) => {
    const count =
      type === "followers" ? profile.followerCount : profile.followingCount;
    if (count <= 0) return;

    setFollows([]);
    setModalVisibility(true);

    const fetchFn =
      type === "followers"
        ? fetchFollowerProfilesForAdmin
        : fetchFollowingProfilesForAdmin;

    const profiles = await fetchProfiles(profile.id, fetchFn);
    setFollows(profiles);
  };

  const onFollowersClick = (profile: ProfileType) => {
    handleFollowClick(profile, "followers");
  };

  const onFollowingClick = (profile: ProfileType) => {
    handleFollowClick(profile, "followings");
  };

  const ModalContent = () => (
    <>
      {/* Username */}
      <div className="flex min-w-[350px] content-between items-center justify-between rounded-t-2xl border-b border-gray-200 bg-darkgray p-3 dark:border-white-800 dark:text-white">
        <div
          className="absolute right-3 cursor-pointer text-black dark:text-white"
          onClick={() => {
            setModalVisibility(false);
            setFollows([]);
          }}
        >
          <FiX size={24} />
        </div>
        <h2 className="flex w-full justify-center text-xl font-semibold capitalize text-black dark:text-white">
          ({profile?.displayName ?? ""})
        </h2>
      </div>

      {/* Follows */}
      <div
        className="flex max-h-[50vh] flex-col overflow-y-auto border-2 border-none bg-darkgray p-2 text-black dark:text-white"
        onDragOver={(e) => e.preventDefault()}
      >
        {follows.length > 0 ? (
          follows?.map((follow: FollowProfileType, index) => (
            <div
              key={index}
              className="w-full rounded-md p-2 hover:bg-slate-200"
            >
              <Link
                href={`/admin/profiles/${follow?.id}/edit`}
                className="w-full py-2"
              >
                <div className="flex w-full items-center">
                  <ResponsiveImage
                    src={follow?.avatar_url || DEFAULT_AVATAR_URL}
                    alt="Notification image"
                    size={[48, 48]}
                    rounded
                    cover
                  />

                  <div className="ml-3 grow break-all">
                    <div className="line-clamp-1 font-semibold text-black-100 dark:text-white-100">
                      {truncateName({
                        name: follow?.display_name,
                        limit: 30,
                      })}
                    </div>
                    <div className="text-sm text-white-500">
                      @
                      {truncateName({
                        name: follow?.username,
                        limit: 30,
                      })}
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))
        ) : (
          <div className="flex min-h-[48vh] items-center justify-center">
            <Loading size={7} />
          </div>
        )}
      </div>
    </>
  );

  return (
    <>
      <div className="flex flex-col items-start justify-end gap-4 lg:flex-row">
        {/* Followers : Button */}
        <button
          disabled={profile.followerCount === 0}
          onClick={() => onFollowersClick(profile)}
          className={`font-bold ${
            profile.followerCount ? "underline" : "hover:cursor-default"
          }`}
        >
          {`Followers (${profile.followerCount})`}
        </button>

        {/* Followings : Button */}
        <button
          disabled={profile.followingCount === 0}
          onClick={() => onFollowingClick(profile)}
          className={`font-bold ${
            profile.followingCount ? "underline" : "hover:cursor-default"
          }`}
        >
          {`Followings (${profile.followingCount})`}
        </button>
      </div>

      {/* Followers & Followings : Modal */}
      <ModalBox
        isOpen={modalVisibility}
        setIsOpen={setModalVisibility}
        ModalContents={ModalContent}
        maxWidth={500}
      />
    </>
  );
};

export default AdminProfileFollowsModalButton;
