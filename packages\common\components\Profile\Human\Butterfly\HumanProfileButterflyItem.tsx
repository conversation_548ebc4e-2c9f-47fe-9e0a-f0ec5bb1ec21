import { Dispatch, use<PERSON>allback, useState } from "react";
import { FiMoreHorizontal, FiSettings, FiShare2 } from "react-icons/fi";
import { Menu } from "@headlessui/react";

import { DEFAULT_AVATAR_URL } from "common/config/constants";
import { isCapacitor } from "common/utils/Helper";
import ResponsiveImage from "common/components/ResponsiveImage";
import Link from "common/utils/Link";
import { embedsShareDataAtom, useFeed } from "common/contexts/FeedContext";
import { ActionRequiresAuthLink } from "common/utils/ActionRequiresAuthLink";
import { useSetAtom } from "jotai";
import ImgElement from "common/utils/ImgElement";
import { Icons } from "common/assets/IconLoader";
import { trackEvent } from "common/utils/trackEvent";

interface ButterflyItemProps {
  isUserAnonymous: boolean;
  isMine: boolean;
  bot: {
    profiles: {
      id: number;
      username: string;
      display_name: string;
      avatar_url: string;
      description: string;
      nsfw: string;
    };
    clone_id: number;
  };
  dismiss?: () => void;
  setIsOpen: Dispatch<boolean>;
  isReview?: boolean;
}

const HumanProfileButterflyItem = ({
  isMine,
  bot,
  dismiss,
  setIsOpen,
  isUserAnonymous,
  isReview,
}: ButterflyItemProps) => {
  const { setIsOpenShareEmbedsModal } = useFeed();
  const setEmbedsShareData = useSetAtom(embedsShareDataAtom);

  const [aspectRatio, setAspectRatio] = useState<number>(1);

  const handleMoreClick = (e: any) => {
    if (isUserAnonymous) {
      return;
    }
    trackEvent("profile.bots.more_clicked", {
      bot_profile_id: bot?.profiles?.id,
      item_type: isReview ? "with_proposed_post" : "regular",
      is_owned_by_user_profile: isMine,
    });
    const path_name = `/users/${bot.profiles.username}`;
    setEmbedsShareData({
      embeds: { ...bot.profiles, bots: { clone_id: bot?.clone_id } },
      link: path_name,
      type: "profile",
      aspectRatio,
      source: "owner_profile",
    });

    if (isCapacitor()) {
      setIsOpen(true);
    }
    e.preventDefault();
  };

  const onClick = useCallback(() => {
    trackEvent("profile.bots.item_clicked", {
      bot_profile_id: bot?.profiles?.id,
      item_type: isReview ? "with_proposed_post" : "regular",
      is_owned_by_user_profile: isMine,
    });
  }, [bot?.profiles?.id, isMine, isReview]);

  return (
    <ActionRequiresAuthLink
      href={`/users/${bot.profiles?.username}`}
      action="view_bot"
      source="profile"
      isUserAnonymous={isUserAnonymous}
      disableInNative={true}
      marketingFocus={{ profile: bot.profiles }}
      onClick={onClick}
    >
      <div
        onClick={dismiss}
        className={`${
          isCapacitor()
            ? "active:bg-gray-200 dark:active:bg-white-800"
            : "hover:bg-gray-200 dark:hover:bg-white-800"
        } flex items-center justify-between rounded-2xl bg-lightgray-100 py-3 pl-3 pr-5 dark:bg-white-700`}
      >
        <div className="relative aspect-square min-h-[36px] min-w-[36px]">
          <ResponsiveImage
            src={bot?.profiles.avatar_url || DEFAULT_AVATAR_URL}
            alt="avatar"
            size={[36, 36]}
            rounded
            cover
            nsfw={isMine ? "normal" : bot.profiles?.nsfw}
            className="aspect-square"
            setAspectRatio={setAspectRatio}
          />
          {isReview && (
            <div className="absolute -bottom-[5px] -right-[5px] flex h-4 w-4 items-center justify-center rounded-full bg-lightpurple-500 outline outline-[3px] outline-white dark:outline-[#181818]">
              <ImgElement
                className="h-2.5 w-2.5"
                src={Icons.NotificationWhiteIcon}
              />
            </div>
          )}
        </div>

        <div className="ml-3 mr-5 flex grow flex-col items-start break-all">
          <div className="txt-black-p1 line-clamp-1">
            {bot.profiles?.display_name}
          </div>

          <div className="txt-gray-p1">
            {bot?.profiles?.post_count
              ? bot?.profiles?.post_count[0]?.count
              : 0}{" "}
            Posts
          </div>
        </div>

        

        <div className="shrink-0">
          <Menu as="div" className="flex h-full">
            {({ open, close }) => (
              <>
                <Menu.Button
                  className="rounded-lg text-gray-500 focus:outline-none"
                  onClick={isCapacitor() ? handleMoreClick : undefined}
                >
                  <FiMoreHorizontal size={22} color="gray" />
                </Menu.Button>
                {open && !isCapacitor() && (
                  <Menu.Items
                    className={`absolute right-4 z-10 ${
                      isMine ? "-mt-8" : "-mt-4"
                    } w-52 rounded-lg border border-gray-200 bg-darkgray text-default shadow-lg dark:border-white-900`}
                  >
                    {isMine && (
                      <Link href={`/bots/${bot?.profiles?.id}/settings`}>
                        <Menu.Item>
                          {({ active }) => (
                            <button
                              className={`flex w-full items-center space-x-2 rounded-t-lg px-4 py-2 text-sm ${
                                active ? "bg-lightgray" : ""
                              }`}
                            >
                              <FiSettings className="h-4 w-4" />
                              <span>Edit Butterfly Settings</span>
                            </button>
                          )}
                        </Menu.Item>
                      </Link>
                    )}

                    <Menu.Item>
                      {({ active }) => (
                        <button
                          onClick={(e) => {
                            setIsOpenShareEmbedsModal(true);
                            close();
                            e.preventDefault();
                          }}
                          className={`flex space-x-2 ${
                            isMine ? "rounded-b-lg" : "rounded-lg"
                          } w-full items-center px-4 py-2 text-sm ${
                            active ? "bg-lightgray" : ""
                          }`}
                        >
                          <FiShare2 className="h-4 w-4" />
                          <span>Share Profile</span>
                        </button>
                      )}
                    </Menu.Item>
                  </Menu.Items>
                )}
              </>
            )}
          </Menu>
        </div>
      </div>
    </ActionRequiresAuthLink>
  );
};

export default HumanProfileButterflyItem;
