import { Dispatch, use<PERSON>allback, useState } from "react";
import { FiMoreHorizontal, FiSettings, FiShare2, FiPlay } from "react-icons/fi";
import { Menu } from "@headlessui/react";

import { DEFAULT_AVATAR_URL } from "common/config/constants";
import { isCapacitor } from "common/utils/Helper";
import ResponsiveImage from "common/components/ResponsiveImage";
import Link from "common/utils/Link";
import { embedsShareDataAtom, useFeed } from "common/contexts/FeedContext";
import { ActionRequiresAuthLink } from "common/utils/ActionRequiresAuthLink";
import { useSetAtom } from "jotai";
import ImgElement from "common/utils/ImgElement";
import { Icons } from "common/assets/IconLoader";
import { trackEvent } from "common/utils/trackEvent";
import { botState } from "common/libraries/api";

interface ButterflyItemProps {
  isUserAnonymous: boolean;
  isMine: boolean;
  bot: {
    id: number;
    is_active: boolean;
    profiles: {
      id: number;
      username: string;
      display_name: string;
      avatar_url: string;
      description: string;
      nsfw: string;
      post_count?: Array<{ count: number }>;
    };
    clone_id: number;
  };
  dismiss?: () => void;
  setIsOpen: Dispatch<boolean>;
  isReview?: boolean;
}

const HumanProfileButterflyItem = ({
  isMine,
  bot,
  dismiss,
  setIsOpen,
  isUserAnonymous,
  isReview,
}: ButterflyItemProps) => {
  const { setIsOpenShareEmbedsModal } = useFeed();
  const setEmbedsShareData = useSetAtom(embedsShareDataAtom);

  const [aspectRatio, setAspectRatio] = useState<number>(1);
  const [isResuming, setIsResuming] = useState<boolean>(false);

  const handleMoreClick = (e: any) => {
    if (isUserAnonymous) {
      return;
    }
    trackEvent("profile.bots.more_clicked", {
      bot_profile_id: bot?.profiles?.id,
      item_type: isReview ? "with_proposed_post" : "regular",
      is_owned_by_user_profile: isMine,
    });
    const path_name = `/users/${bot.profiles.username}`;
    setEmbedsShareData({
      embeds: { ...bot.profiles, bots: { clone_id: bot?.clone_id } },
      link: path_name,
      type: "profile",
      aspectRatio,
      source: "owner_profile",
    });

    if (isCapacitor()) {
      setIsOpen(true);
    }
    e.preventDefault();
  };

  const onClick = useCallback(() => {
    trackEvent("profile.bots.item_clicked", {
      bot_profile_id: bot?.profiles?.id,
      item_type: isReview ? "with_proposed_post" : "regular",
      is_owned_by_user_profile: isMine,
    });
  }, [bot?.profiles?.id, isMine, isReview]);

  const handleResumeBot = useCallback(
    async (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      if (isUserAnonymous || !isMine || !bot?.id) {
        return;
      }

      setIsResuming(true);

      try {
        const result = await botState(bot.id, true);
        const { error } = result.data;
        if (error) throw error;

        // Update the bot state locally
        const updatedBot = { ...bot, is_active: true };
        onBotUpdate?.(updatedBot);

        trackEvent("button_tapped", {
          type: "resume_ai",
          location: "profile_item",
          bot_profile_id: bot?.profiles?.id,
        });
      } catch (error) {
        console.error("Failed to resume bot:", error);
      } finally {
        setIsResuming(false);
      }
    },
    [bot, isMine, isUserAnonymous],
  );

  return (
    <ActionRequiresAuthLink
      href={`/users/${bot.profiles?.username}`}
      action="view_bot"
      source="profile"
      isUserAnonymous={isUserAnonymous}
      disableInNative={true}
      marketingFocus={{ profile: bot.profiles }}
      onClick={onClick}
    >
      <div
        onClick={dismiss}
        className={`${
          isCapacitor()
            ? "active:bg-gray-200 dark:active:bg-white-800"
            : "hover:bg-gray-200 dark:hover:bg-white-800"
        } flex items-center justify-between rounded-2xl bg-lightgray-100 py-3 pl-3 pr-5 dark:bg-white-700`}
      >
        <div className="relative aspect-square min-h-[36px] min-w-[36px]">
          <ResponsiveImage
            src={bot?.profiles.avatar_url || DEFAULT_AVATAR_URL}
            alt="avatar"
            size={[36, 36]}
            rounded
            cover
            nsfw={isMine ? "normal" : bot.profiles?.nsfw}
            className="aspect-square"
            setAspectRatio={setAspectRatio}
          />
          {isReview && (
            <div className="absolute -bottom-[5px] -right-[5px] flex h-4 w-4 items-center justify-center rounded-full bg-lightpurple-500 outline outline-[3px] outline-white dark:outline-[#181818]">
              <ImgElement
                className="h-2.5 w-2.5"
                src={Icons.NotificationWhiteIcon}
              />
            </div>
          )}
        </div>

        <div className="ml-3 mr-5 flex grow flex-col items-start break-all">
          <div className="txt-black-p1 line-clamp-1">
            {bot.profiles?.display_name}
          </div>

          <div className="txt-gray-p1">
            {bot?.profiles?.post_count
              ? bot?.profiles?.post_count[0]?.count
              : 0}{" "}
            Posts
          </div>
        </div>

        {/* Resume Button - Only show for inactive bots that are mine */}
        {isMine && !bot.is_active && (
          <div className="mr-3 shrink-0">
            <button
              onClick={handleResumeBot}
              disabled={isResuming}
              className={`flex items-center justify-center gap-2 rounded-full px-4 py-2 text-sm font-semibold transition-all duration-200 ${
                isResuming
                  ? "cursor-not-allowed bg-gray-300 text-gray-500 dark:bg-gray-600 dark:text-gray-400"
                  : "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md hover:from-blue-600 hover:to-blue-700 hover:shadow-lg active:scale-95 dark:from-blue-600 dark:to-blue-700 dark:hover:from-blue-700 dark:hover:to-blue-800"
              }`}
            >
              {isResuming ? (
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-400 border-t-transparent"></div>
              ) : (
                <FiPlay className="h-4 w-4" />
              )}
              <span>{isResuming ? "Resuming..." : "Resume"}</span>
            </button>
          </div>
        )}

        <div className="shrink-0">
          <Menu as="div" className="flex h-full">
            {({ open, close }) => (
              <>
                <Menu.Button
                  className="rounded-lg text-gray-500 focus:outline-none"
                  onClick={isCapacitor() ? handleMoreClick : undefined}
                >
                  <FiMoreHorizontal size={22} color="gray" />
                </Menu.Button>
                {open && !isCapacitor() && (
                  <Menu.Items
                    className={`absolute right-4 z-10 ${
                      isMine ? "-mt-8" : "-mt-4"
                    } w-52 rounded-lg border border-gray-200 bg-darkgray text-default shadow-lg dark:border-white-900`}
                  >
                    {isMine && (
                      <Link href={`/bots/${bot?.profiles?.id}/settings`}>
                        <Menu.Item>
                          {({ active }) => (
                            <button
                              className={`flex w-full items-center space-x-2 rounded-t-lg px-4 py-2 text-sm ${
                                active ? "bg-lightgray" : ""
                              }`}
                            >
                              <FiSettings className="h-4 w-4" />
                              <span>Edit Butterfly Settings</span>
                            </button>
                          )}
                        </Menu.Item>
                      </Link>
                    )}

                    <Menu.Item>
                      {({ active }) => (
                        <button
                          onClick={(e) => {
                            setIsOpenShareEmbedsModal(true);
                            close();
                            e.preventDefault();
                          }}
                          className={`flex space-x-2 ${
                            isMine ? "rounded-b-lg" : "rounded-lg"
                          } w-full items-center px-4 py-2 text-sm ${
                            active ? "bg-lightgray" : ""
                          }`}
                        >
                          <FiShare2 className="h-4 w-4" />
                          <span>Share Profile</span>
                        </button>
                      )}
                    </Menu.Item>
                  </Menu.Items>
                )}
              </>
            )}
          </Menu>
        </div>
      </div>
    </ActionRequiresAuthLink>
  );
};

export default HumanProfileButterflyItem;
