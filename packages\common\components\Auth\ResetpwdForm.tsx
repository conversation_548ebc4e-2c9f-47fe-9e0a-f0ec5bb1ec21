"use client";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useHistory } from "react-router-dom";

import supabase from "common/libraries/supabaseClient";
import Link from "common/utils/Link";
import { isCapacitor } from "common/utils/Helper";
import { useNextRouting } from "common/contexts/NextRoutingContext";
import { useToast } from "common/contexts/ToastContext";

const validationSchema = Yup.object({
  password: Yup.string()
    .min(8, "Must be at least 8 characters")
    .required("Required"),
  confirmpassword: Yup.string()
    .min(8, "Must be at least 8 characters")
    .required("Required"),
});

const formFields = ["password", "confirmpassword"] as const;

const formFieldTypes = {
  password: "password",
  confirmpassword: "password",
};

const formFieldPlaceholders = {
  password: "Password",
  confirmpassword: "Confirm Password",
};

const formFieldAutocomplete = {
  password: "new-password",
  confirmpassword: "new-password",
};

export default function ResetpwdForm({ setSignin }: any) {
  const { invokeToast } = useToast();
  let router;

  if (isCapacitor()) {
    router = useHistory();
  } else {
    router = useNextRouting();
  }

  const formik = useFormik({
    initialValues: {
      password: "",
      confirmpassword: "",
    },
    validationSchema,
    onSubmit: async (values, { setSubmitting, setErrors }) => {
      if (values.password == values.confirmpassword) {
        const { data, error } = await supabase.auth.updateUser({
          password: values.password,
        });
        if (error) {
          invokeToast({
            type: "error",
            text: "Something went Wrong!",
            position: "top",
          });
          setErrors({ supabase: error.message });
        } else {
          invokeToast({
            type: "info",
            text: "Password changed successfuly!",
            position: "top",
          });
          setSubmitting(false);
          router.push("/users/signin/email");
        }
      } else {
        invokeToast({
          type: "error",
          text: "Please confirm your password!",
          position: "top",
        });
      }
    },
  });

  return (
    <div className="mx-auto w-full max-w-sm">
      <form
        onSubmit={formik.handleSubmit}
        className="mb-4 rounded bg-white px-8 pb-8 pt-6 shadow-md"
      >
        <div className="mb-4 text-center">
          <h2 className="mb-2 text-2xl font-bold">Reset Password</h2>
        </div>

        {formFields.map((field, index) => (
          <div className="mb-4" key={index}>
            <input
              {...formik.getFieldProps(field)}
              className="focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
              id={field}
              type={formFieldTypes[field]}
              placeholder={formFieldPlaceholders[field]}
              autoComplete={formFieldAutocomplete[field]}
            />
            {formik.touched[field] && formik.errors[field] ? (
              <div className="text-sm text-red-500">{formik.errors[field]}</div>
            ) : null}
          </div>
        ))}
        <div className="flex items-center justify-between">
          <button
            type="submit"
            className="focus:shadow-outline w-full rounded bg-black px-4 py-3 font-semibold text-white hover:bg-blue-700 focus:outline-none"
            disabled={formik.isSubmitting}
          >
            Reset
          </button>
        </div>
        {formik.errors.supabase && (
          <div className="mt-4 text-sm text-red-500">
            {formik.errors.supabase}
          </div>
        )}
      </form>
      <p className="text-center text-sm">
        Have an account?
        <Link
          href="/users/signin/email"
          className="ml-1 text-blue-500 hover:text-blue-800"
          onClick={() => {
            if (setSignin) {
              setSignin(true);
            }
          }}
        >
          Log in
        </Link>
      </p>
    </div>
  );
}
