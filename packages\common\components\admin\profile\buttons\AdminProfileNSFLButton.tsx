import { FiRotate<PERSON>w, Fi<PERSON>lertOctagon } from "react-icons/fi";

import { updateProfile } from "common/libraries/api";
import { adminActionLogs } from "common/libraries/adminApi";

const AdminProfileNSFLButton = ({
  profile,
  profiles,
  setProfiles,
}: {
  profile: any;
  profiles: any;
  setProfiles: any;
}) => {
  const onMarkasNSFLClick = async (profileId: number, isNSFL: boolean) => {
    const confirmMessage = isNSFL
      ? "Are you sure you want to mark this profile as Normal?"
      : "Are you sure you want to mark this profile as NSFL?";

    if (!confirm(confirmMessage)) return;

    try {
      const updatedProfiles = profiles.map((p: any) => {
        if (p.id === profileId) {
          return {
            ...p,
            nsfl: !isNSFL,
            visibility: isNSFL ? "public" : "hidden",
          };
        }
        return p;
      });

      setProfiles(updatedProfiles);

      const updatePayload = {
        nsfl: !isNSFL,
        visibility: isNSFL ? "public" : "hidden",
      };

      const {
        data: { data, error },
      } = await updateProfile({
        updateContents: updatePayload,
        profileId,
      });

      if (error) throw error;

      await adminActionLogs({
        target: "profiles",
        action: isNSFL ? "unmark_as_nsfl" : "mark_as_nsfl",
        info: [
          {
            table: "profiles",
            id: profileId,
            old: { nsfl: isNSFL },
            new: { nsfl: !isNSFL },
          },
        ],
      });
    } catch (error) {
      alert(`Failed to ${isNSFL ? "unmark" : "mark"} as NSFL: ${error}`);
    }
  };

  return (
    <button
      className={`flex items-center gap-1 rounded border px-2 py-1 text-xs ${
        profile?.nsfl == true
          ? "bg-gray-600 text-gray-100"
          : "border border-red-600 bg-[#fcff3a] text-red-600"
      }`}
      onClick={() => {
        onMarkasNSFLClick(profile?.id, profile?.nsfl);
      }}
    >
      {profile?.nsfl ? <FiRotateCw /> : <FiAlertOctagon />}
      {profile?.nsfl == true ? "Unmark NSFL" : "Mark NSFL"}
    </button>
  );
};

export default AdminProfileNSFLButton;
