"use client";
import { useFormik } from "formik";
import { useContext, useEffect, useState } from "react";
import * as Yup from "yup";
import { useHistory } from "react-router-dom";

import { UserContext } from "common/contexts/UserContext";
import supabase from "common/libraries/supabaseClient";
import Link from "common/utils/Link";
import { isCapacitor } from "common/utils/Helper";
import { useNextRouting } from "common/contexts/NextRoutingContext";
import { SITE_URL } from "common/config";
import ResponsiveImage from "../ResponsiveImage";
import { LOGO_AVATAR_URL } from "common/config/constants";
import { useTheme } from "common/contexts/ThemeContext";
import Loading from "../Loading";
import { useFeatureFlagEnabled } from "common/utils/posthog";
import { removeItemStorage } from "common/utils/localStorageWrappers";

const validationSchema = Yup.object({
  email: Yup.string().email("Invalid email address").required("Required"),
  fullName: Yup.string().required("Required"),
  password: Yup.string()
    .min(8, "Must be at least 8 characters")
    .required("Required"),
});

const formFields = ["email", "fullName", "password"] as const;

const formFieldTypes = {
  email: "email",
  fullName: "text",
  password: "password",
};

const formFieldPlaceholders = {
  email: "Email",
  fullName: "Full Name",
  password: "Password",
};

const formFieldAutocomplete = {
  email: "email",
  fullName: "name",
  password: "new-password",
};

export default function SignupForm({ setSignin }) {
  const { loadUser } = useContext(UserContext);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { theme } = useTheme();
  let router: any;

  if (isCapacitor()) {
    router = useHistory();
  } else {
    router = useNextRouting();
  }

  const isWaitlistEnabled = useFeatureFlagEnabled("WAITLIST_ENABLED");

  const formik = useFormik({
    initialValues: {
      email: "",
      fullName: "",
      password: "",
    },
    validationSchema,
    onSubmit: async (values, { setSubmitting, setErrors }) => {
      removeItemStorage("returnUrl");
      setIsLoading(true);
      const { data, error } = await supabase.auth.signUp({
        email: values.email,
        password: values.password,
        options: {
          data: {
            full_name: values.fullName,
          },
          emailRedirectTo: SITE_URL,
        },
      });
      if (error) {
        setIsLoading(false);
        console.error("error", error);
        setErrors({ supabase: error.message });
      } else {
        try {
          const { error } = await supabase.auth.signInWithPassword({
            email: values.email,
            password: values.password,
          });

          if (error) throw error;
        } catch (error) {
          console.error(error);
        }
        setIsLoading(false);
        await loadUser();

        if (isWaitlistEnabled) {
          router.push("/waitlist");
        } else {
          router.push("/age");
        }
      }
      setSubmitting(false);
    },
  });

  useEffect(() => {
    if (isCapacitor()) return;

    if (isWaitlistEnabled) {
      router?.prefetch("/waitlist");
    } else {
      router?.prefetch("/age");
    }
  }, [router]);

  return (
    <div className="mx-auto w-full max-w-sm text-black dark:bg-black-100 dark:text-white">
      <form
        onSubmit={formik.handleSubmit}
        className="mb-4 rounded bg-white px-8 pb-8 pt-6 shadow-md dark:bg-black-100"
      >
        <div className="mb-2 flex items-end justify-center gap-1">
          <ResponsiveImage
            autoWidth
            src={LOGO_AVATAR_URL}
            alt="Notification image"
            size={[48, 48]}
            className="!w-12 !bg-transparent"
            cover
          />
          <h2 className="text-2xl font-extrabold text-black dark:text-white">
            Butterflies
          </h2>
        </div>
        <p className="mb-3 text-center text-sm font-bold">
          Sign up to explore the world of butterflies
        </p>

        {formFields.map((field, index) => (
          <div className="mb-4" key={index}>
            <input
              {...formik.getFieldProps(field)}
              className="focus:shadow-outline w-full appearance-none rounded border bg-white px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none dark:bg-black-100 dark:text-white"
              id={field}
              type={formFieldTypes[field]}
              placeholder={formFieldPlaceholders[field]}
              autoComplete={formFieldAutocomplete[field]}
            />
            {formik.touched[field] && formik.errors[field] ? (
              <div className="text-sm text-red-500">{formik.errors[field]}</div>
            ) : null}
          </div>
        ))}
        <p className="mb-2 text-sm">
          By signing up, you agree to our{" "}
          <Link
            href="/privacy"
            className="cursor-pointer text-blue-500 underline"
          >
            Privacy Policy
          </Link>{" "}
          and{" "}
          <Link
            href="/terms"
            className="cursor-pointer text-blue-500 underline"
          >
            Terms of Service
          </Link>
          .
        </p>
        <div className="flex items-center justify-between">
          <button
            type="submit"
            className={`focus:shadow-outline flex w-full flex-row items-center justify-center space-x-2 rounded px-4 py-3 font-semibold text-white hover:bg-blue-700 focus:outline-none dark:text-black ${
              isLoading ? "bg-gray-500" : "bg-black-100 dark:bg-white"
            }`}
            disabled={formik.isSubmitting || isLoading}
          >
            <span>Sign up</span>
            {isLoading ? (
              <Loading
                size={3}
                style={
                  theme === "dark" ? { color: "black" } : { color: "white" }
                }
              />
            ) : (
              <></>
            )}
          </button>
        </div>
        {formik.errors.supabase && (
          <div className="mt-4 text-sm text-red-500">
            {formik.errors.supabase}
          </div>
        )}
      </form>
      <p className="text-center text-sm text-black dark:text-white">
        Have an account?
        <Link
          href="/users/signin/email"
          className="ml-1 text-blue-500 hover:text-blue-800"
          onClick={() => {
            if (setSignin) {
              setSignin(true);
            }
          }}
        >
          Log in
        </Link>
      </p>
    </div>
  );
}
