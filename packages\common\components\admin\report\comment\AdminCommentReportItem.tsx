"use client";
import Link from "next/link";
import { FiExternalLink, FiDelete } from "react-icons/fi";

import ResponsiveImage from "common/components/ResponsiveImage";
import AdminCommentReportDoneButton from "common/components/admin/report/comment/AdminCommentReportDoneButton";
import { removeComment } from "common/libraries/adminApi";
import { Dispatch } from "react";
import { DEFAULT_AVATAR_URL } from "common/config/constants";

export interface AdminCommentReportType {
  id: number;
  post: {
    id: number;
    imageURL: string;
    description: string;
    slug: string;
  };
  comment: {
    id: number | null;
    content: string | null;
  };
  reason: {
    types: [string];
    userComment: string;
  };
  reporteeProfile: {
    id: number;
    avatarURL: string;
    name: string;
    username: string;
    description: string;
    nsfw: string;
    nsfl: boolean;
  };
  reporterProfile: {
    id: number;
    avatarURL: string;
    name: string;
    username: string;
  };
}

interface CommentReportItemProps {
  report: AdminCommentReportType;
  setReports: Dispatch<React.SetStateAction<AdminCommentReportType[]>>;
}

const AdminCommentReportItem = ({
  report,
  setReports,
}: CommentReportItemProps) => {
  /// handle "Delete Comment" button click
  const handleDeleteCommentClick = (commentID: number | null) => {
    /// validate comment ID
    if (!commentID) return;

    /// add confirmation
    const isConfirmed = confirm(
      "Are you sure you want to remove this comment?",
    );

    if (!isConfirmed) return;

    /// remove comment
    removeComment({ id: commentID }).then((res) => {
      if (res.status === 200) {
        /// handle success
        setReports((prev: AdminCommentReportType[]) => {
          return prev.map((report) => {
            if (report.comment.id === commentID) {
              return {
                ...report,
                comment: {
                  id: null,
                  content: report.comment.content,
                },
              };
            } else {
              return report;
            }
          });
        });
      } else {
        /// handle error
        console.error("Error occured while removing comment");
      }
    });
  };

  return (
    <div className="mx-auto flex w-full max-w-full flex-col justify-start gap-4 bg-white text-xs dark:bg-black-100">
      <div className="focus:shadow-outline relative mt-3 w-full space-y-4 overflow-hidden rounded-lg border border-gray-200 bg-white text-left text-base font-medium text-gray-900 shadow-sm hover:shadow-lg focus-visible:ring focus-visible:ring-gray-500/75 dark:border-gray-500 dark:bg-slate-900 dark:text-white">
        <div className="relative flex w-full justify-between">
          <div className="flex w-full max-w-full flex-col lg:flex-row">
            <div className="flex flex-col gap-2 sm:w-1/2 md:flex-row">
              {/* Post */}
              <div className="min-w-20 relative flex w-full flex-shrink-0 md:h-72 md:w-56 md:flex-none">
                {/* Post image */}
                <div className="h-48 w-32 shrink-0 justify-center overflow-hidden md:h-full md:w-full">
                  <ResponsiveImage
                    src={report.post.imageURL}
                    alt={"Post image"}
                    size={[224, 288]}
                  />
                </div>

                <div className="flex h-48 w-full flex-col gap-2 border-b border-r bg-white bg-opacity-95 p-3 opacity-100 transition-opacity duration-100 hover:opacity-100 md:absolute md:top-0 md:h-full md:opacity-0">
                  {/* Post ID */}
                  <div className="flex gap-1">
                    <b>ID:</b>
                    <Link
                      href={`/admin/posts/${report.post.id}/edit`}
                      as={`/admin/posts/${report.post.id}/edit`}
                      className="link"
                    >
                      {report.post.id}
                    </Link>
                  </div>

                  {/* Post Description */}
                  <div className="flex flex-col overflow-y-auto">
                    Description:
                    <p className="text-gray-500">{report.post.description}</p>
                  </div>
                </div>
              </div>

              <div className="flex w-full grow-0 border-b md:border-r">
                {/* Comment & Report */}
                <div className="relative flex max-w-full flex-1 flex-col gap-2.5 p-3">
                  {/* Reportee Profile */}
                  <div className="flex h-fit w-fit min-w-[20rem] items-center gap-2 overflow-hidden rounded-l-[2.75rem] rounded-r-xl border border-red-200 bg-red-50 p-1 pr-4">
                    {/* Reportee Avatar */}
                    <div className="min-w-10 h-20 w-20 flex-shrink-0 justify-center overflow-hidden rounded-full">
                      <ResponsiveImage
                        src={
                          report.reporteeProfile.avatarURL || DEFAULT_AVATAR_URL
                        }
                        alt={"Reportee avatar"}
                        size={[80, 80]}
                      />
                    </div>

                    <div className="z-10 flex flex-col gap-0.5">
                      {/* Reportee ID */}
                      <div className="flex items-center gap-1">
                        <p>ID:</p>
                        <Link
                          href="/admin/profiles/[id]"
                          as={`/admin/profiles/${report.reporteeProfile.id}`}
                          className="link"
                        >
                          {report.reporteeProfile.id}
                        </Link>

                        {/* Reportee Profile NSFW */}
                        {report.reporteeProfile.nsfw === "nsfw" && (
                          <div className="ml-2 rounded-xl border border-red-400 bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-gray-700 dark:text-red-400">
                            NSFW
                          </div>
                        )}

                        {/* Reportee Profile NSFL */}
                        {report.reporteeProfile.nsfl == true && (
                          <div className="rounded-xl border border-[#ff4747] bg-[#fcff3a] px-2.5 py-0.5 text-xs font-medium text-[#ff4747] dark:bg-gray-700 dark:text-red-500">
                            NSFL
                          </div>
                        )}
                      </div>

                      {/* Reportee Name */}
                      <div className="flex gap-1">
                        <p>Name:</p>
                        <p className="line-clamp-1 max-w-[48rem]">
                          {report?.reporteeProfile.name}
                        </p>
                      </div>

                      {/* Reportee Username & NSFW & NSFL */}
                      <div className="flex items-center gap-1">
                        {/* Reportee Username */}
                        <p>Username:</p>
                        <Link
                          href="/users/[id]"
                          as={`/users/${report.reporteeProfile.username}`}
                          className="link"
                        >
                          {report.reporteeProfile.username}
                        </Link>
                      </div>
                    </div>
                  </div>

                  {/* Comment Content */}
                  <div className="flex flex-1 flex-col justify-between gap-2">
                    {report.comment.id ? (
                      <>
                        <p className="max-h-32 max-w-full overflow-x-auto overflow-y-auto rounded-2xl bg-gray-100 px-3 py-2 text-black">
                          {report.comment.content}
                        </p>

                        {/* Comment ID */}
                        {report.comment.id && (
                          <div className="flex w-full items-center justify-end gap-2">
                            {/* <p>ID: {report.comment.id}</p> */}

                            <div className="flex gap-2">
                              {/* View comment */}
                              <Link
                                target="_blank"
                                href={`/users/[username]/p/[slug]?comment_id=[comment_id]`}
                                as={`/users/${report.reporterProfile.username}/p/${report.post.slug}?comment_id=${report.comment.id}`}
                                className="flex w-fit items-center gap-1 rounded-full border border-blue-600 px-2 py-0.5 text-blue-600 transition-colors hover:bg-blue-600 hover:text-white"
                              >
                                <div className="text-sm font-bold">View</div>
                                <FiExternalLink />
                              </Link>

                              {/* Remove comment */}
                              <button
                                onClick={() => {
                                  handleDeleteCommentClick(report.comment.id);
                                }}
                                className="flex items-center gap-1 rounded-full border border-red-600 px-2 py-0.5 text-sm font-bold text-red-600 transition-colors hover:bg-red-600 hover:text-white"
                              >
                                <FiDelete /> Remove
                              </button>
                            </div>
                          </div>
                        )}
                      </>
                    ) : (
                      <i className="text-gray-500">
                        This comment is removed by Admin
                        {report.comment.content && (
                          <p className="max-h-32 max-w-full overflow-x-auto overflow-y-auto rounded-2xl bg-gray-100 px-3 py-2 text-gray-500">{`(${report.comment.content})`}</p>
                        )}
                      </i>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Reporter */}
            <div className="flex flex-col gap-2 p-4 pb-10 sm:w-1/2">
              {/* Reporter Profile */}
              <div className="relative flex h-fit w-fit min-w-[20rem] items-center gap-2 overflow-hidden rounded-l-[2.75rem] rounded-r-xl border border-blue-200 bg-blue-50 p-1 pr-4">
                {/* Reporter Avatar */}
                <div className="min-w-10 h-20 w-20 flex-shrink-0 justify-center overflow-hidden rounded-full">
                  <ResponsiveImage
                    src={report.reporterProfile.avatarURL || DEFAULT_AVATAR_URL}
                    alt={"Reporter avatar"}
                    size={[80, 80]}
                  />
                </div>

                <div className="flex flex-col gap-0.5">
                  {/* Reporter ID */}
                  <div className="flex gap-1">
                    <p>ID:</p>
                    <Link
                      href="/admin/profiles/[id]"
                      as={`/admin/profiles/${report.reporterProfile.id}`}
                      className="link"
                    >
                      {report.reporterProfile.id}
                    </Link>
                  </div>

                  {/* Reporter Name */}
                  <div className="flex gap-1">
                    <p>Name:</p>
                    <p className="line-clamp-1 max-w-[48rem]">
                      {report?.reporterProfile.name}
                    </p>
                  </div>

                  {/* Reporter Username */}
                  <div className="flex gap-1">
                    <p>Username:</p>
                    <Link
                      href="/users/[id]"
                      as={`/users/${report.reporterProfile.username}`}
                      className="link"
                    >
                      {report.reporterProfile.username}
                    </Link>
                  </div>
                </div>
              </div>

              {/* Reason Comment */}
              <p className="w-fit rounded-md bg-red-200 px-3">
                {report.reason.userComment}
              </p>

              {/* Reason Types */}
              <div className="flex max-w-full flex-wrap gap-2">
                {report.reason.types.map((type: string, index: number) => {
                  return (
                    <div
                      key={index}
                      className="w-fit rounded-full bg-gray-200 px-3"
                    >
                      {type}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          <div className="absolute bottom-3 right-3 justify-end">
            {/* Done : Button */}
            <AdminCommentReportDoneButton
              reportID={report.id}
              setReports={setReports}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminCommentReportItem;
