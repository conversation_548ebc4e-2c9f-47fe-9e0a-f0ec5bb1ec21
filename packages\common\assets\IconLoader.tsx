// @ts-nocheck
// IconLoader.js

import React from "react";

// Importing SVG icons
import HomeDarkIcon from "common/assets/Dark/home-icon.svg";
import HomeIconDarkFilled from "common/assets/Dark/home-icon-filled.svg";
import SearchIconDarkFilled from "common/assets/Dark/search-icon-filled.svg";
import MailIconDarkFilled from "common/assets/Dark/mail-icon-filled.svg";
import SearchDarkIcon from "common/assets/Dark/search-icon.svg";
import AddDarkIcon from "common/assets/Dark/add-icon.svg";
import MailDarkIcon from "common/assets/Dark/mail-icon.svg";
import ProfileDarkIcon from "common/assets/Dark/profile-icon.svg";
import ProfileFilledDarkIcon from "common/assets/Dark/profile-filled-icon.svg";
import BackDarkIcon from "common/assets/Dark/back-icon.svg";
import NewMessageIconDark from "common/assets/Dark/new-message-icon.svg";

import HomeIcon from "common/assets/Light/home-icon.svg";
import HomeIconFilled from "common/assets/Light/home-icon-filled.svg";
import SearchIconFilled from "common/assets/Light/search-icon-filled.svg";
import MailIconFilled from "common/assets/Light/mail-icon-filled.svg";
import SearchIcon from "common/assets/Light/search-icon.svg";
import AddIcon from "common/assets/Light/add-icon.svg";
import MailIcon from "common/assets/Light/mail-icon.svg";
import ProfileIcon from "common/assets/Light/profile-icon.svg";
import ProfileFilledIcon from "common/assets/Light/profile-filled-icon.svg";
import LogoIcon from "common/assets/Light/logo.svg";
import LogoIconColored from "common/assets/<EMAIL>";
import BackIcon from "common/assets/Light/back-icon.svg";
import NewMessageIcon from "common/assets/Light/new-message-icon.svg";
import ChatIcon from "common/assets/Light/chat-icon.svg";

import HeartIcon from "common/assets/Light/<EMAIL>";
import HeartIconFilled from "common/assets/Light/<EMAIL>";
import HeartIconFilledV1 from "common/assets/heart-icon-filled-v1.svg";

import HeartMenuIcon from "common/assets/Light/heart-menu-icon.svg";
import HeartMenuIconFilled from "common/assets/Light/heart-menu-icon-filled.svg";

import HeartMenuDarkIcon from "common/assets/Dark/heart-menu-icon.svg";
import HeartMenuDarkIconFilled from "common/assets/Dark/heart-menu-icon-filled.svg";

import HeartDarkIcon from "common/assets/Dark/<EMAIL>";
import HeartIconDarkFilled from "common/assets/Dark/<EMAIL>";
import ThumbsDownIconDark from "common/assets/Dark/icon-dislike.svg";
import ThumbsDownIcon from "common/assets/Light/icon-dislike.svg";

import Realistic from "common/assets/Styles/realistic.jpg";
import RealisticV2 from "common/assets/Styles/realistic_v2.jpg";
import SemiRealistic from "common/assets/Styles/semi_realistic.jpg";
import Drawing from "common/assets/Styles/drawing.jpg";

import CommentDarkIcon from "common/assets/Dark/comment-icon.svg";
import CommentDarkIconFilled from "common/assets/Dark/comment-icon-filled.png";
import CommentIcon from "common/assets/Light/comment-icon.svg";
import CommentEditIcon from "common/assets/Light/comment-edit-icon.svg";
import CommentEditDarkIcon from "common/assets/Dark/comment-edit-icon.svg";

import SendIcon from "common/assets/Light/send-icon-2.svg";

import SendIconFilled from "common/assets/Light/send-icon.svg";

import BookmarkDarkIcon from "common/assets/Dark/bookmark-icon.svg";
import BookmarkDarkIconFilled from "common/assets/Dark/bookmark-icon-filled.svg";
import BookmarkIcon from "common/assets/Light/bookmark-icon.svg";
import BookmarkIconFilled from "common/assets/Light/bookmark-icon-filled.svg";

import RedditIcon from "common/assets/reddit.svg";
import DiscordIcon from "common/assets/discord.svg";

import PricingIcon from "common/assets/Light/pricing-icon.svg";
import PricingIconFilled from "common/assets/Light/pricing-icon-filled.svg";
import PricingDarkIcon from "common/assets/Dark/pricing-icon.svg";
import PricingDarkIconFilled from "common/assets/Dark/pricing-icon-filled.svg";

// location icon
import LocationIcon from "common/assets/Light/location-icon.svg";
import LocationDarkIcon from "common/assets/Dark/location-icon.svg";

import ArrowIcon from "common/assets/Light/arrow-icon.svg";
import ArrowDarkIcon from "common/assets/Dark/arrow-icon.svg";

import DirectionUpDarkIcon from "common/assets/Dark/direction-up-icon.svg";

// Sign in with email icon
import EmailIcon from "common/assets/Light/email-icon.svg";
import EmailIconFilled from "common/assets/Light/email-icon-filled-white.svg";

import EmailDarkIcon from "common/assets/Dark/email-icon.svg";
import EmailDarkIconFilled from "common/assets/Dark/email-icon-filled.svg";

// Sign in with phone icon
import PhoneIcon from "common/assets/Light/phone-icon.svg";
import PhoneIconWhiteFilled from "common/assets/Light/phone-icon-white.svg";

import PhoneDarkIcon from "common/assets/Dark/phone-icon.svg";

import BrowsingIcon from "common/assets/Light/browsing-icon.svg";
import BrowsingDarkIcon from "common/assets/Dark/browsing-icon.svg";

//Code icon
import CodeIcon from "common/assets/Light/code-icon-white.svg";

// arrow under and up icon
import ArrowUnderIcon from "common/assets/Light/arrow-under-icon.svg";

import ArrowUnderDarkIcon from "common/assets/Dark/arrow-under-icon.svg";

import ArrowRightIcon from "common/assets/Light/arrow-right-icon.svg";
import ArrowRightDarkIcon from "common/assets/Dark/arrow-right-icon.svg";

import CameraDarkIcon from "common/assets/Dark/camera-icon.svg";
import CameraIcon from "common/assets/Light/camera-icon.svg";
import CameraIconPrimaryCTA from "common/assets/snappy-camera/camera-icon-primary-cta.svg";
import CameraUnsupportedIcon from "common/assets/Light/camera-unsupported-icon.svg";

// password icon
import PassIconFilled from "common/assets/Light/pass-icon-filled-white.svg";
import PassDarkIconFilled from "common/assets/Dark/pass-icon-filled.svg";

// Onboarding stuff
import OnboardingBirthdayIcon from "common/assets/Light/onboarding-birthday-icon-white.svg";
import OnboardingBirthdayDarkIcon from "common/assets/Dark/onboarding-birthday-icon.svg";
import OnboardingNameIcon from "common/assets/Light/onboarding-name-icon-white.svg";
import OnboardingNameDarkIcon from "common/assets/Dark/onboarding-name-icon.svg";
import ContactSyncIcon from "common/assets/reskin-onboarding/contact-sync.svg";

import FilledCheckmarkIcon from "common/assets/Light/filled-checkmark.svg";
import FilledWarningIcon from "common/assets/Light/filled-warning.svg";

import EmptyFileIcon from "common/assets/Light/empty-file-icon.svg";
import EmptyFileDarkIcon from "common/assets/Dark/empty-file-icon.svg";

import EmptyPostIcon from "common/assets/Light/empty-post-icon.svg";
import EmptyPostDarkIcon from "common/assets/Dark/empty-post-icon.svg";

import PhotosIcon from "common/assets/Light/photos-icon.svg";
import PhotosDarkIcon from "common/assets/Dark/photos-icon.svg";
import AlarmIcon from "common/assets/Light/alarm-icon.svg";
import EmptySearchIcon from "common/assets/Light/empty-search-icon.svg";

import BookMarkIconV2 from "common/assets/Light/bookmark-icon-2.svg";
import BookMarkDarkIconV2 from "common/assets/Dark/bookmark-icon-2.svg";

import SettingIcon from "common/assets/Light/setting-icon.svg";
import SettingDarkIcon from "common/assets/Dark/setting-icon.svg";

import ArrowUnderIconV2 from "common/assets/Light/arrow-under-icon-2.svg";
import ArrowUnderDarkIconV2 from "common/assets/Dark/arrow-under-icon-2.svg";

import SubscribeIcon from "common/assets/Light/subscribe-icon.svg";
import SubscribeDarkIcon from "common/assets/Dark/subscribe-icon.svg";
import SubscribeFilledIcon from "common/assets/Light/subscribe-filled-icon.svg";
import SubscribeFilledDarkIcon from "common/assets/Dark/subscribe-filled-icon.svg";

import ShareIcon from "common/assets/Light/share-icon.svg";
import ShareDarkIcon from "common/assets/Dark/share-icon.svg";

import InboxIcon from "common/assets/Light/inbox-icon.svg";
import InboxDarkIcon from "common/assets/Dark/inbox-icon.svg";
import InboxFilledIcon from "common/assets/Light/inbox-icon-filled.svg";
import InboxFilledDarkIcon from "common/assets/Dark/inbox-icon-filled.svg";

import PokeIcon from "common/assets/Light/poke-icon.svg";
import ImageIcon from "common/assets/Light/image-icon.svg";
import DownloadIcon from "common/assets/Light/download-icon.svg";

import BookMarksEmptyIcon from "common/assets/Light/bookmarks-empty-icon.svg";
import BookMarksEmptyDarkIcon from "common/assets/Dark/bookmarks-empty-icon.svg";
import BlockProfileIcon from "common/assets/Light/block-icon.svg";
import BlockProfileDarkIcon from "common/assets/Dark/block-icon.svg";
import BlockProfileWhiteIcon from "common/assets/Light/block-profile-white-icon.svg";
import BlockProfileRedIcon from "common/assets/Light/block-profile-red-icon.svg";
import UnblockProfileIcon from "common/assets/Light/unblock-profile-icon.svg";
import UnblockProfileDarkIcon from "common/assets/Dark/unblock-profile-icon.svg";
import UnblockInfoIcon from "common/assets/Light/unblock-info-icon.svg";
import UnblockInfoDarkIcon from "common/assets/Dark/unblock-info-icon.svg";
import HideIcon from "common/assets/Light/hide-icon.svg";
import HideDarkIcon from "common/assets/Dark/hide-icon.svg";
import ReportIcon from "common/assets/Light/report-icon.svg";
import ReportBlackIcon from "common/assets/Light/report-black-icon.svg";
import ReportWhiteIcon from "common/assets/Light/report-white-icon.svg";
import ReportGrayIcon from "common/assets/Dark/report-gray-icon.svg";
import ReportRobotIcon from "common/assets/Light/report-robot-icon.svg";
import ReportRobotDarkIcon from "common/assets/Dark/report-robot-icon.svg";
import ReportSoundIcon from "common/assets/Light/report-sound-icon.svg";
import ReportSoundDarkIcon from "common/assets/Dark/report-sound-icon.svg";
import ReportWarningIcon from "common/assets/Light/report-warning-icon.svg";
import ReportWarningDarkIcon from "common/assets/Dark/report-warning-icon.svg";
import ReportImpersonationIcon from "common/assets/Light/report-impersonation-icon.svg";
import ReportImpersonationDarkIcon from "common/assets/Dark/report-impersonation-icon.svg";
import ReportInappropriateIcon from "common/assets/Light/report-inappropriate-icon.svg";
import ReportInappropriateDarkIcon from "common/assets/Dark/report-inappropriate-icon.svg";
import ReportToastIcon from "common/assets/Light/report-toast-icon.svg";
import ReportToastDarkIcon from "common/assets/Dark/report-toast-icon.svg";
import UserIcon from "common/assets/Light/user-icon.svg";
import UserDarkIcon from "common/assets/Dark/user-icon.svg";

import PlusCircleIcon from "common/assets/Light/plus-circle-icon.svg";
import PlusCircleDarkIcon from "common/assets/Dark/plus-circle-icon.svg";

import WhitePlusIcon from "common/assets/Light/white-plus-icon.svg";
import WhitePlusDarkIcon from "common/assets/Dark/white-plus-icon.svg";

import ActiveCommentIcon from "common/assets/Active/active-comment-icon.svg";
import ActiveHeartIcon from "common/assets/Active/active-heart-icon.svg";
import ActivePenIcon from "common/assets/Active/active-pen-icon.svg";
import ActiveUserIcon from "common/assets/Active/active-user-icon.svg";

import InteractNewIcon from "common/assets/Interact/interact-new-icon.svg";
import InteractPokeIcon from "common/assets/Interact/interact-poke-icon.svg";
import ImageWhiteIcon from "common/assets/Light/image-white-icon.svg";
import PokeToastIcon from "common/assets/Interact/poke-toast-icon.svg";
import InteractChatIcon from "common/assets/Interact/interact-chat-icon.svg";
import InteractFriendIcon from "common/assets/Interact/interact-friend-icon.svg";
import InteractPokeImageChatIcon from "common/assets/Interact/interact-poke-image-chat-icon.svg";
import FirstPostStarsIcon from "common/assets/Interact/first-post-stars.svg";
import FirstPostColumnLine from "common/assets/Interact/first-post-column-line.svg";
import FirstPostButterflyIcon from "common/assets/Interact/first-post-butterfly-icon.svg";

import ButterflyWhiteIcon from "common/assets/butterfly-white-icon.svg";
import ButterflyWhiteBigIcon from "common/assets/Light/butterflies-logo-white.svg";
import CopyLinkIcon from "common/assets/Light/copy-link-icon.svg";
import CopyLinkDarkIcon from "common/assets/Dark/copy-link-icon.svg";
import DownloadWhiteIcon from "common/assets/Light/download-icon-white.svg";
import MessageIcon from "common/assets/Light/message-icon.svg";
import NewLogoIconColored from "common/assets/new_logo.png";
import SnapchatIcon from "common/assets/Light/snapchat-icon.svg";
import InstagramIcon from "common/assets/Light/instagram-icon.svg";
import QrCodeLogoIcon from "common/assets/Light/qr-code-logo.svg";
import EmptyChatlistIcon from "common/assets/Light/empty-chatlist-icon.svg";
import EmptyChatlistDarkIcon from "common/assets/Dark/empty-chatlist-icon.svg";

import ChatRequestIcon from "common/assets/Light/chat-request-icon.svg";
import ChatRequestDarkIcon from "common/assets/Dark/chat-request-icon.svg";
import InfoIcon from "common/assets/Light/info-icon.svg";
import InfoDarkIcon from "common/assets/Dark/info-icon.svg";
import DeleteIcon from "common/assets/Light/delete-icon.svg";
import DeleteWhiteIcon from "common/assets/Light/delete-white-icon.svg";

import PhotoIcon from "common/assets/Light/photo-icon.svg";
import PhotoDarkIcon from "common/assets/Dark/photo-icon.svg";
import MicIcon from "common/assets/Light/mic-icon.svg";
import MicDarkIcon from "common/assets/Dark/mic-icon.svg";

import ReskinOnboardingBackButton from "common/assets/reskin-onboarding/reskin-onboarding-back-button.svg";
import ReskinOnboardingDarkBackButton from "common/assets/reskin-onboarding/reskin-onboarding-dark-back-button.svg";

import ReskinOnboardingUpIcon from "common/assets/reskin-onboarding/reskin-onboarding-up-icon.svg";
import ReskinOnboardingClockIcon from "common/assets/reskin-onboarding/reskin-onboarding-clock-icon.svg";

import ReskinOnboardingProfilePreviewLocationIcon from "common/assets/reskin-onboarding/profile-preview-location-icon.svg";
import NotificationPromptDeviceOutline from "common/assets/reskin-onboarding/notification-prompt-device-outline.svg";
import NotificationPromptDeviceOutlineDark from "common/assets/reskin-onboarding/notification-prompt-device-outline-dark.svg";
import NotificationPromptBellIcon from "common/assets/reskin-onboarding/notification-prompt-bell-icon.svg";
import NotificationPromptBellIconDark from "common/assets/reskin-onboarding/notification-prompt-bell-icon-dark.svg";
import NotificationPromptAppIcon44 from "common/assets/reskin-onboarding/<EMAIL>";
import WelcomeCharacterIcon from "common/assets/reskin-onboarding/welcome-character-icon.svg";
import WelcomeCharacterDarkIcon from "common/assets/reskin-onboarding/welcome-character-dark-icon.svg";
import WelcomeInteractIcon from "common/assets/reskin-onboarding/welcome-interact-icon.svg";
import WelcomeFlutterIcon from "common/assets/reskin-onboarding/welcome-flutter-icon.svg";
import CloneSkipIcon from "common/assets/reskin-onboarding/clone-skip-icon.svg";
import AccountPreviewIcon from "common/assets/reskin-onboarding/account-preview-icon.svg";
import AccountPreviewDarkIcon from "common/assets/reskin-onboarding/account-preview-dark-icon.svg";

import EditProfileLocationIcon from "common/assets/Light/edit-profile-location-icon.svg";
import EditProfileLocationDarkIcon from "common/assets/Dark/edit-profile-location-icon.svg";
import EditProfileBioIcon from "common/assets/Light/<EMAIL>";
import EditProfileBioDarkIcon from "common/assets/Dark/<EMAIL>";
import EditProfileGenderIcon from "common/assets/Light/edit-profile-gender-icon.svg";
import EditProfileGenderDarkIcon from "common/assets/Dark/edit-profile-gender-icon.svg";
import EditProfileUsernameIcon from "common/assets/Light/edit-profile-username-icon.svg";
import EditProfileUsernameDarkIcon from "common/assets/Dark/edit-profile-username-icon.svg";
import EditProfileUploadBlueIcon from "common/assets/Light/<EMAIL>";
import EditProfileUploadWhiteIcon from "common/assets/Dark/<EMAIL>";
import EditProfileCalendarIcon from "common/assets/Light/edit-profile-calendar-icon.svg";
import EditProfileCalendarDarkIcon from "common/assets/Dark/edit-profile-calendar-icon.svg";
import CheckMarkCircleIcon from "common/assets/Light/check-mak-circle-icon.svg";
import CheckMarkCircleLightIcon from "common/assets/Light/check-mark-circle-light-outline.svg";
import CheckMarkCircleDarkIcon from "common/assets/Dark/check-mark-circle-dark-outline.svg";
import NotificationCircleIcon from "common/assets/Light/notification-circle-icon.svg";
import NotificationCircleDarkIcon from "common/assets/Dark/notification-circle-icon.svg";
import NotificationWhiteIcon from "common/assets/Light/notification-white-icon.svg";
import ClipboardIcon from "common/assets/Light/clipboard-icon.svg";
import NSFWEyeIcon from "common/assets/Light/nsfw-eye-icon.svg";
import AddProfileLocationActiveIcon from "common/assets/Light/profile-add-location-active-icon.svg";
import AddProfileLocationUnActiveIcon from "common/assets/Light/profile-add-location-unactive-icon.svg";

import LocationFilledDarkIcon from "common/assets/Dark/location-filled-icon.svg";

import VoiceFixIcon from "common/assets/Light/voice-fix-icon.svg";
import VoicePlayIcon from "common/assets/Light/voice-play-icon.svg";

import NewChatIcon from "common/assets/Light/new-chat-icon.svg";
import ChatEditCloseIcon from "common/assets/Light/chat-edit-close-icon.svg";
import ChatEditCloseDarkIcon from "common/assets/Dark/chat-edit-close-icon.svg";
import ChatReadAllIcon from "common/assets/Light/chat-read-all-icon.svg";
import ChatReadAllDarkIcon from "common/assets/Dark/chat-read-all-icon.svg";

import CloseIcon from "common/assets/Light/close-icon.svg";
import CloseDarkIcon from "common/assets/Dark/close-icon.svg";

import ClockIcon from "common/assets/Light/clock-icon.svg";
import ClockDarkIcon from "common/assets/Dark/clock-icon.svg";

import ImageIcon2 from "common/assets/Light/image-icon2.svg";

import WarningIcon from "common/assets/Light/warning-icon.svg";
import WarningDarkIcon from "common/assets/Dark/warning-icon.svg";

import PrivateIcon from "common/assets/Light/private-icon.svg";
import PrivateDarkIcon from "common/assets/Dark/private-icon.svg";
import CheckMarkWhiteIcon from "common/assets/Light/check-mark-white-icon.svg";
import FollowIcon from "common/assets/Light/follow-icon.svg";
import FollowDarkIcon from "common/assets/Dark/follow-icon.svg";
import UnfollowIcon from "common/assets/Light/unfollow-icon.svg";
import UnfollowDarkIcon from "common/assets/Dark/unfollow-icon.svg";
import FollowToastIcon from "common/assets/Light/follow-toast-icon.svg";
import UnfollowToastIcon from "common/assets/Light/unfollow-toast-icon.svg";

import NotifyIconV1 from "common/assets/Light/notify-icon-v1.svg";
import NotifyDarkIconV1 from "common/assets/Dark/notify-icon-v1.svg";

import CommentIconV1 from "common/assets/Light/comment-icon-v1.svg";
import CommentDarkIconV1 from "common/assets/Dark/comment-icon-v1.svg";

import PokeIconV1 from "common/assets/Light/poke-icon-v1.svg";
import PokeDarkIconV1 from "common/assets/Dark/poke-icon-v1.svg";

import ShareIconV1 from "common/assets/Light/share-icon-v1.svg";
import ShareDarkIconV1 from "common/assets/Dark/share-icon-v1.svg";

// clone
import CloneIcon from "common/assets/clone/clone-icon.svg";
import BlackCloneIcon from "common/assets/clone/black-clone-icon.svg";
import CloneStyleIcon from "common/assets/clone/style-icon.svg";
import CloneStyleDarkIcon from "common/assets/clone/style-dark-icon.svg";
import CloneCameraWhiteIcon from "common/assets/clone/camera-white-icon.svg";
import CloneCameraBlackIcon from "common/assets/clone/camera-black-icon.svg";
import CameraAccessIcon from "common/assets/clone/camera-access-icon.svg";
import CameraAccessDarkIcon from "common/assets/clone/camera-access-dark-icon.svg";
import CameraDeniedIcon from "common/assets/clone/camera-denied-icon.svg";
import CameraDeniedDarkIcon from "common/assets/clone/camera-denied-dark-icon.svg";
import CameraSettingsIcon from "common/assets/clone/camera-settings-icon.svg";
import CameraSettingsDarkIcon from "common/assets/clone/camera-settings-dark-icon.svg";
import WarningMarkIcon from "common/assets/clone/warning-mark-icon.svg";
import WarningMarkBigIcon from "common/assets/clone/warning-mark-big-icon.svg";
import FrontFaceEmojiIcon from "common/assets/clone/front-face-emoji-icon.svg";
import FrontFaceEmojiDarkIcon from "common/assets/clone/front-face-emoji-dark-icon.svg";
import LeftFaceEmojiIcon from "common/assets/clone/left-face-emoji-icon.svg";
import LeftFaceEmojiDarkIcon from "common/assets/clone/left-face-emoji-dark-icon.svg";
import RightFaceEmojiIcon from "common/assets/clone/right-face-emoji-icon.svg";
import RightFaceEmojiDarkIcon from "common/assets/clone/right-face-emoji-dark-icon.svg";
import CloneLeftArrowIcon from "common/assets/clone/left-arrow-icon.svg";
import CloneLeftArrowDarkIcon from "common/assets/clone/left-arrow-dark-icon.svg";
import CloneRightArrowIcon from "common/assets/clone/right-face-icon.svg";
import CloneRightArrowDarkIcon from "common/assets/clone/right-face-dark-icon.svg";
import CloneTransitionIcon from "common/assets/clone/transition.png";
import ClonePhoneIcon from "common/assets/clone/phone.svg";
import PopoverArrowIcon from "common/assets/clone/popover-arrow.svg";

import GoogleIcon from "common/assets/Light/google-icon.svg";
import GoogleDarkIcon from "common/assets/Dark/google-icon.svg";

import NewPostCircleImage_1 from "common/assets/Imagegroup/NewPostCircleImage_1.png";
import NewPostCircleImage_2 from "common/assets/Imagegroup/NewPostCircleImage_2.png";
import NewPostCircleImage_3 from "common/assets/Imagegroup/NewPostCircleImage_3.png";
import RegenPFPIcon from "common/assets/Light/regen-pfp-icon.svg";
import RegenPFPDarkIcon from "common/assets/Dark/regen-pfp-icon.svg";
import EditIcon from "common/assets/Light/edit-icon.svg";
import EditDarkIcon from "common/assets/Dark/edit-icon.svg";
import ReportFlagIcon from "common/assets/Light/report-flag-icon.svg";
import ReportFlagDarkIcon from "common/assets/Dark/report-flag-icon.svg";
import ReportFlagIconV2 from "common/assets/Light/report-flag-icon-v2.svg";
import ReportFlagDarkIconV2 from "common/assets/Dark/report-flag-icon-v2.svg";
import ReportFlagWhiteIcon from "common/assets/Light/report-flag-white-icon.svg";
import ReportNotInterestingIcon from "common/assets/Light/report-not-interesting-icon.svg";
import ReportNotInterestingDarkIcon from "common/assets/Dark/report-not-interesting-icon.svg";
import ReportDeformedIcon from "common/assets/Light/report-deformed-icon.svg";
import ReportDeformedDarkIcon from "common/assets/Dark/report-deformed-icon.svg";
import ReportNudityIcon from "common/assets/Light/report-nudity-icon.svg";
import ReportNudityDarkIcon from "common/assets/Dark/report-nudity-icon.svg";
import ReportNotMatchingIcon from "common/assets/Light/report-not-matching-icon.svg";
import ReportNotMatchingDarkIcon from "common/assets/Dark/report-not-matching-icon.svg";
import NSFWPFPGroupIcon from "common/assets/Light/<EMAIL>";
import NSFWPFPGroupDarkIcon from "common/assets/Dark/<EMAIL>";

import RegenIcon from "common/assets/Light/regen-icon.svg";
import RegenDarkIcon from "common/assets/Dark/regen-icon.svg";

import EyeIcon from "common/assets/Light/eye-icon.svg";
import EyeDarkIcon from "common/assets/Dark/eye-icon.svg";

import ResembleIcon from "common/assets/Light/resemble-icon.svg";
import ResembleDarkIcon from "common/assets/Dark/resemble-icon.svg";

import NotMatchingCaptionIcon from "common/assets/Light/not-matching-caption-icon.svg";
import NotMatchingCaptionDarkIcon from "common/assets/Dark/not-matching-caption-icon.svg";

import VerticalSettingIcon from "common/assets/Light/vertical-setting-icon.svg";
import VerticalSettingDarkIcon from "common/assets/Dark/vertical-setting-icon.svg";

import NotFoundIcon from "common/assets/Light/not-found-icon.svg";
import NotFoundDarkIcon from "common/assets/Dark/not-found-icon.svg";

import CloseRedIcon from "common/assets/Light/close-red-icon.svg";

import DismissWarningIcon from "common/assets/Light/dismiss-warning.svg";
import DismissWarningDarkIcon from "common/assets/Dark/dismiss-warning.svg";
import MentionAtSignIcon from "common/assets/Light/mention-at-sign-icon-light.svg";
import MentionAtSignDarkIcon from "common/assets/Dark/mention-at-sign-icon-dark.svg";
import MentionTagWarningIcon from "common/assets/Light/mention-tag-warning-icon-light.svg";
import MentionTagWarningDarkIcon from "common/assets/Dark/mention-tag-warning-icon-dark.svg";

import DisclosureArrowIcon from "common/assets/Light/disclosure-arrow-light.svg";
import DisclosureArrowDarkIcon from "common/assets/Dark/disclosure-arrow-dark.svg";

import UnblockProfileRedIcon from "common/assets/Light/unblock-profile-red-icon.svg";

import WatermarkLogo from "common/assets/watermark-logo.svg";

import TransferToAppDesktopRings from "common/assets/TransferToApp/desktop-rings.png";
import TransferToAppDesktopGlowBlur from "common/assets/TransferToApp/desktop-glow-blur.png";
import TransferToAppDesktopCloseIcon from "common/assets/TransferToApp/desktop-close-icon.svg";
import TransferToAppMobileRings from "common/assets/TransferToApp/mobile-rings.png";
import TransferToAppMobileGlowBlur from "common/assets/TransferToApp/mobile-glow-blur.png";
import TransferToAppMobileCloseIcon from "common/assets/TransferToApp/mobile-close-icon.svg";
import PlainAppleAppStoreIcon from "common/assets/stores/plain-apple-app-store.svg";
import PlainGooglePlayStoreIcon from "common/assets/stores/plain-google-play-store.svg";

import RepostRocketOutlineDark from "common/assets/Dark/repost-rocket-outline-dark.svg";
import RepostRocketOutlineLight from "common/assets/Light/repost-rocket-outline-light.svg";
import RepostRocketFill from "common/assets/Universal/repost-rocket-fill.svg";
import LoadingAnimationIcon from "common/assets/Universal/loading.svg";

import LightningYellowIcon from "common/assets/lightning_yellow.svg";
import LightningRedIcon from "common/assets/lightning_red.svg";
import RecVideoIcon from "common/assets/rec_video.svg";

import RegenerateIcon from "common/assets/regenerate-icon.svg";

import NewRedHeartIcon from "common/assets/new-red-heart.svg";
import ContactSettingsIcon from "common/assets/Light/contact-settings-icon.svg";
import ContactSettingsDarkIcon from "common/assets/Dark/contact-settings-icon.svg";
import LockTagIcon from "common/assets/Light/lock-tag-icon.svg";
import SuggestedTagIcon from "common/assets/Light/suggested-tag-icon.svg";
import TrendingTagIcon from "common/assets/Light/trending-tag-icon.svg";
import TaggedTagIcon from "common/assets/Light/tagged-tag-icon.svg";
import SimilarTagIcon from "common/assets/Light/similar-tag-icon.svg";
import FollowedTagIcon from "common/assets/Light/followed-tag-icon.svg";
import CreatedTagIcon from "common/assets/Light/created-tag-icon.svg";
import LockTagDarkIcon from "common/assets/Dark/lock-tag-icon.svg";
import SuggestedTagDarkIcon from "common/assets/Dark/suggested-tag-icon.svg";
import TrendingTagDarkIcon from "common/assets/Dark/trending-tag-icon.svg";
import TaggedTagDarkIcon from "common/assets/Dark/tagged-tag-icon.svg";
import SimilarTagDarkIcon from "common/assets/Dark/similar-tag-icon.svg";
import FollowedTagDarkIcon from "common/assets/Dark/followed-tag-icon.svg";
import CreatedTagDarkIcon from "common/assets/Dark/created-tag-icon.svg";
import NextPostIcon from "common/assets/Light/next-post-icon.svg";
import NextPostDarkIcon from "common/assets/Dark/next-post-icon.svg";

import FatButtonPassIcon from "common/assets/ProposedPostReview/fat-button-pass-icon.svg";
import FatButtonPostIcon from "common/assets/ProposedPostReview/fat-button-post-icon.svg";
import PassConfirmIcon from "common/assets/ProposedPostReview/pass-confirm-icon.svg";

import LightningXPDarkIcon from "common/assets/Dark/lightning-xp.svg";
import LightningXPLightIcon from "common/assets/Light/lightning-xp.svg";

import ChallengeFlagIcon from "common/assets/challenge_flag.svg";
import ChallengeArtLightIcon from "common/assets/Light/challenge-art-icon.svg";
import ChallengeArtDarkIcon from "common/assets/Dark/challenge-art-icon.svg";
import ChallengeUserLightIcon from "common/assets/Light/challenge-user-icon.svg";
import ChallengeUserDarkIcon from "common/assets/Dark/challenge-user-icon.svg";
import ChallengeXPLightIcon from "common/assets/Light/challenge-xp-icon.svg";
import ChallengeXPDarkIcon from "common/assets/Dark/challenge-xp-icon.svg";

import RankingOneIcon from "common/assets/ranking-one-icon.svg";
import RankingSecondIcon from "common/assets/ranking-two-icon.svg";
import RankingThreeIcon from "common/assets/ranking-three-icon.svg";

import ExpandIcon from "common/assets/expand-icon.svg";

import RankingStarIcon from "common/assets/ranking-star-icon.svg";
import JoinFlagIcon from "common/assets/join_flag.svg";
import LeaderboardEmptyDarkIcon from "common/assets/Dark/leaderboard-empty-icon.svg";
import LeaderboardEmptyLightIcon from "common/assets/Light/leaderboard-empty-icon.svg";

import ReviewPostIcon from "common/assets/review-post-icon.svg";

import VoteEmptyLightIcon from "common/assets/Light/vote-empty-icon.svg";
import VoteEmptyDarkIcon from "common/assets/Dark/vote-empty-icon.svg";
import PastLeaderboardEmptyLightIcon from "common/assets/Light/past-leaderboard-empty.svg";
import PastLeaderboardEmptyDarkIcon from "common/assets/Dark/past-leaderboard-empty.svg";
import GeneratingPostLightIcon from "common/assets/Light/generating_post.svg";
import GeneratingPostDarkIcon from "common/assets/Dark/generating_post.svg";
import RefineStarIcon from "common/assets/Light/refine-star-icon.svg";
import VariationIcon from "common/assets/Light/variation-icon.svg";
import CircleWaveWhiteIcon from "common/assets/Light/circle-wave-white-icon.svg";
import RefinePostClockIcon from "common/assets/Light/refine-post-clock-icon.svg";
import RefineCaptionIcon from "common/assets/Light/refine-caption-icon.svg";
import InputExpandIcon from "common/assets/Light/expand-icon.svg";
import InputShrinkIcon from "common/assets/Light/shrink-icon.svg";
import InputExpandDarkIcon from "common/assets/Dark/expand-icon.svg";
import InputShrinkDarkIcon from "common/assets/Dark/shrink-icon.svg";
import DownVoteIcon from "common/assets/downvote-icon.svg";
import UpVoteIcon from "common/assets/upvote-icon.svg";
import VoteGradientBorder from "common/assets/vote-gradient-border.svg";
import SwipeLeftIcon from "common/assets/swipe-left-icon.svg";
import SwipeLeftArrowIcon from "common/assets/swipe-left-arrow-icon.svg";

export const Icons = {
  HomeIcon,
  HomeIconFilled,
  SearchIconFilled,
  MailIconFilled,
  SearchIcon,
  AddIcon,
  MailIcon,
  ProfileIcon,
  ProfileFilledIcon,
  LogoIcon,
  LogoIconColored,
  BackIcon,
  HeartIcon,
  HeartIconFilled,
  HeartMenuIcon,
  HeartMenuIconFilled,

  HeartMenuDarkIcon,
  HeartMenuDarkIconFilled,

  ThumbsDownIconDark,
  RedditIcon,
  DiscordIcon,
  HomeDarkIcon,
  HomeIconDarkFilled,
  SearchIconDarkFilled,
  MailIconDarkFilled,
  SearchDarkIcon,
  AddDarkIcon,
  MailDarkIcon,
  ProfileDarkIcon,
  ProfileFilledDarkIcon,
  BackDarkIcon,
  HeartDarkIcon,
  HeartIconDarkFilled,

  NewMessageIcon,
  NewMessageIconDark,
  ChatIcon,

  Realistic,
  RealisticV2,
  SemiRealistic,
  Drawing,

  CommentIcon,
  CommentDarkIcon,
  CommentDarkIconFilled,
  CommentEditIcon,
  CommentEditDarkIcon,

  SendIcon,
  ThumbsDownIcon,

  BookmarkDarkIcon,
  BookmarkDarkIconFilled,
  BookmarkIcon,
  BookmarkIconFilled,

  PricingIcon,
  PricingIconFilled,
  PricingDarkIcon,
  PricingDarkIconFilled,

  LocationIcon,
  LocationDarkIcon,

  ArrowIcon,
  ArrowDarkIcon,

  EmailIcon,
  EmailIconFilled,
  EmailDarkIcon,
  EmailDarkIconFilled,

  PhoneIcon,
  PhoneIconWhiteFilled,
  PhoneDarkIcon,
  CodeIcon,

  BrowsingIcon,
  BrowsingDarkIcon,

  ArrowUnderIcon,
  ArrowUnderDarkIcon,
  ArrowRightIcon,
  ArrowRightDarkIcon,

  DirectionUpDarkIcon,

  CameraIcon,
  CameraDarkIcon,
  CameraIconPrimaryCTA,
  CameraUnsupportedIcon,

  PassIconFilled,
  PassDarkIconFilled,

  OnboardingBirthdayIcon,
  OnboardingBirthdayDarkIcon,
  OnboardingNameIcon,
  OnboardingNameDarkIcon,
  ContactSyncIcon,

  FilledCheckmarkIcon,
  FilledWarningIcon,

  EmptyFileIcon,
  EmptyFileDarkIcon,

  EmptyPostIcon,
  EmptyPostDarkIcon,

  PhotosIcon,
  PhotosDarkIcon,
  AlarmIcon,
  EmptySearchIcon,

  BookMarkIconV2,
  BookMarkDarkIconV2,
  SettingIcon,
  SettingDarkIcon,

  ArrowUnderIconV2,
  ArrowUnderDarkIconV2,

  SubscribeIcon,
  SubscribeDarkIcon,
  SubscribeFilledIcon,
  SubscribeFilledDarkIcon,

  ShareIcon,
  ShareDarkIcon,

  InboxIcon,
  InboxDarkIcon,
  InboxFilledIcon,
  InboxFilledDarkIcon,

  PokeIcon,
  ImageIcon,
  DownloadIcon,

  BookMarksEmptyIcon,
  BookMarksEmptyDarkIcon,

  PlusCircleIcon,
  PlusCircleDarkIcon,

  WhitePlusIcon,
  WhitePlusDarkIcon,
  BlockProfileIcon,
  BlockProfileDarkIcon,
  BlockProfileWhiteIcon,
  BlockProfileRedIcon,
  UnblockProfileIcon,
  UnblockProfileDarkIcon,
  UnblockInfoIcon,
  UnblockInfoDarkIcon,
  HideIcon,
  HideDarkIcon,
  ReportIcon,
  ReportBlackIcon,
  ReportWhiteIcon,
  ReportGrayIcon,
  ReportRobotIcon,
  ReportRobotDarkIcon,
  ReportSoundIcon,
  ReportSoundDarkIcon,
  ReportWarningIcon,
  ReportWarningDarkIcon,
  ReportImpersonationIcon,
  ReportImpersonationDarkIcon,
  ReportInappropriateIcon,
  ReportInappropriateDarkIcon,
  ReportToastIcon,
  ReportToastDarkIcon,
  UserIcon,
  UserDarkIcon,

  ActiveCommentIcon,
  ActiveHeartIcon,
  ActivePenIcon,
  ActiveUserIcon,

  InteractNewIcon,
  InteractPokeIcon,
  ImageWhiteIcon,
  PokeToastIcon,
  InteractChatIcon,
  InteractFriendIcon,
  InteractPokeImageChatIcon,
  FirstPostStarsIcon,
  FirstPostColumnLine,
  FirstPostButterflyIcon,

  ButterflyWhiteIcon,
  CopyLinkIcon,
  CopyLinkDarkIcon,
  DownloadWhiteIcon,
  MessageIcon,
  NewLogoIconColored,
  SnapchatIcon,
  InstagramIcon,
  QrCodeLogoIcon,
  EmptyChatlistIcon,
  EmptyChatlistDarkIcon,
  ChatRequestIcon,
  ChatRequestDarkIcon,
  InfoIcon,
  InfoDarkIcon,
  DeleteIcon,
  DeleteWhiteIcon,

  PhotoIcon,
  PhotoDarkIcon,
  MicIcon,
  MicDarkIcon,
  SendIconFilled,

  ReskinOnboardingBackButton,
  ReskinOnboardingDarkBackButton,

  ReskinOnboardingUpIcon,
  ReskinOnboardingClockIcon,

  ReskinOnboardingProfilePreviewLocationIcon,
  WelcomeCharacterIcon,
  WelcomeCharacterDarkIcon,
  WelcomeInteractIcon,
  WelcomeFlutterIcon,

  EditProfileLocationIcon,
  EditProfileLocationDarkIcon,
  EditProfileBioIcon,
  EditProfileBioDarkIcon,
  EditProfileGenderIcon,
  EditProfileGenderDarkIcon,
  EditProfileUsernameIcon,
  EditProfileUsernameDarkIcon,
  EditProfileUploadBlueIcon,
  EditProfileUploadWhiteIcon,
  EditProfileCalendarIcon,
  EditProfileCalendarDarkIcon,
  NotificationPromptDeviceOutline,
  NotificationPromptDeviceOutlineDark,
  NotificationPromptBellIcon,
  NotificationPromptBellIconDark,
  NotificationPromptAppIcon44,
  CheckMarkCircleIcon,
  CheckMarkCircleLightIcon,
  CheckMarkCircleDarkIcon,
  NotificationCircleIcon,
  NotificationCircleDarkIcon,
  NotificationWhiteIcon,
  ClipboardIcon,
  NSFWEyeIcon,
  AddProfileLocationActiveIcon,
  AddProfileLocationUnActiveIcon,

  LocationFilledDarkIcon,

  VoiceFixIcon,
  VoicePlayIcon,

  NewChatIcon,
  ChatEditCloseIcon,
  ChatEditCloseDarkIcon,
  ChatReadAllIcon,
  ChatReadAllDarkIcon,
  CloseIcon,
  CloseDarkIcon,

  ClockIcon,
  ClockDarkIcon,

  ImageIcon2,

  WarningIcon,
  WarningDarkIcon,
  PrivateIcon,
  PrivateDarkIcon,
  CheckMarkWhiteIcon,
  FollowIcon,
  FollowDarkIcon,
  UnfollowIcon,
  UnfollowDarkIcon,
  FollowToastIcon,
  UnfollowToastIcon,

  NotifyIconV1,
  NotifyDarkIconV1,

  CommentIconV1,
  CommentDarkIconV1,

  PokeIconV1,
  PokeDarkIconV1,

  ShareIconV1,
  ShareDarkIconV1,

  HeartIconFilledV1,

  CloneIcon,
  BlackCloneIcon,
  CloneStyleIcon,
  CloneStyleDarkIcon,
  CloneCameraWhiteIcon,
  CloneCameraBlackIcon,
  CameraAccessIcon,
  CameraAccessDarkIcon,
  CameraDeniedIcon,
  CameraDeniedDarkIcon,
  CameraSettingsIcon,
  CameraSettingsDarkIcon,
  WarningMarkIcon,
  WarningMarkBigIcon,
  FrontFaceEmojiIcon,
  FrontFaceEmojiDarkIcon,
  LeftFaceEmojiIcon,
  LeftFaceEmojiDarkIcon,
  RightFaceEmojiIcon,
  RightFaceEmojiDarkIcon,
  CloneLeftArrowIcon,
  CloneLeftArrowDarkIcon,
  CloneRightArrowIcon,
  CloneRightArrowDarkIcon,
  CloneTransitionIcon,
  ClonePhoneIcon,
  ButterflyWhiteBigIcon,
  PopoverArrowIcon,

  GoogleIcon,
  GoogleDarkIcon,

  NewPostCircleImage_1,
  NewPostCircleImage_2,
  NewPostCircleImage_3,
  RegenPFPIcon,
  RegenPFPDarkIcon,
  EditIcon,
  EditDarkIcon,
  ReportFlagIcon,
  ReportFlagDarkIcon,
  ReportFlagWhiteIcon,
  ReportFlagIconV2,
  ReportFlagDarkIconV2,
  ReportNotInterestingIcon,
  ReportNotInterestingDarkIcon,
  ReportDeformedIcon,
  ReportDeformedDarkIcon,
  ReportNudityIcon,
  ReportNudityDarkIcon,
  ReportNotMatchingIcon,
  ReportNotMatchingDarkIcon,
  NSFWPFPGroupIcon,
  NSFWPFPGroupDarkIcon,

  RegenIcon,
  RegenDarkIcon,

  EyeIcon,
  EyeDarkIcon,

  ResembleIcon,
  ResembleDarkIcon,

  NotMatchingCaptionIcon,
  NotMatchingCaptionDarkIcon,

  VerticalSettingIcon,
  VerticalSettingDarkIcon,

  NotFoundIcon,
  NotFoundDarkIcon,

  CloseRedIcon,

  DismissWarningIcon,
  DismissWarningDarkIcon,
  MentionAtSignIcon,
  MentionAtSignDarkIcon,
  MentionTagWarningIcon,
  MentionTagWarningDarkIcon,

  DisclosureArrowIcon,
  DisclosureArrowDarkIcon,

  UnblockProfileRedIcon,
  WatermarkLogo,

  TransferToAppDesktopRings,
  TransferToAppDesktopGlowBlur,
  TransferToAppDesktopCloseIcon,
  TransferToAppMobileRings,
  TransferToAppMobileGlowBlur,
  TransferToAppMobileCloseIcon,
  PlainAppleAppStoreIcon,
  PlainGooglePlayStoreIcon,
  CloneSkipIcon,
  AccountPreviewIcon,
  AccountPreviewDarkIcon,

  RepostRocketOutlineDark,
  RepostRocketOutlineLight,
  RepostRocketFill,
  LoadingAnimationIcon,
  LightningRedIcon,
  LightningYellowIcon,
  RecVideoIcon,
  RegenerateIcon,

  NewRedHeartIcon,
  ContactSettingsIcon,
  ContactSettingsDarkIcon,
  LockTagIcon,
  SuggestedTagIcon,
  TrendingTagIcon,
  TaggedTagIcon,
  SimilarTagIcon,
  FollowedTagIcon,
  CreatedTagIcon,
  LockTagDarkIcon,
  SuggestedTagDarkIcon,
  TrendingTagDarkIcon,
  TaggedTagDarkIcon,
  SimilarTagDarkIcon,
  FollowedTagDarkIcon,
  CreatedTagDarkIcon,
  LightningXPLightIcon,
  LightningXPDarkIcon,
  ChallengeFlagIcon,
  ChallengeArtLightIcon,
  ChallengeArtDarkIcon,
  ChallengeUserLightIcon,
  ChallengeUserDarkIcon,
  ChallengeXPLightIcon,
  ChallengeXPDarkIcon,
  RankingOneIcon,
  RankingSecondIcon,
  RankingThreeIcon,
  RankingStarIcon,

  ExpandIcon,
  JoinFlagIcon,
  LeaderboardEmptyDarkIcon,
  LeaderboardEmptyLightIcon,

  NextPostIcon,
  NextPostDarkIcon,
  FatButtonPassIcon,
  FatButtonPostIcon,
  PassConfirmIcon,

  ReviewPostIcon,
  VoteEmptyLightIcon,
  VoteEmptyDarkIcon,
  PastLeaderboardEmptyLightIcon,
  PastLeaderboardEmptyDarkIcon,
  GeneratingPostDarkIcon,
  GeneratingPostLightIcon,
  RefineStarIcon,
  VariationIcon,
  CircleWaveWhiteIcon,
  RefinePostClockIcon,
  RefineCaptionIcon,
  InputExpandIcon,
  InputShrinkIcon,
  InputExpandDarkIcon,
  InputShrinkDarkIcon,
  DownVoteIcon,
  UpVoteIcon,
  VoteGradientBorder,
  SwipeLeftArrowIcon,
  SwipeLeftIcon,
};
