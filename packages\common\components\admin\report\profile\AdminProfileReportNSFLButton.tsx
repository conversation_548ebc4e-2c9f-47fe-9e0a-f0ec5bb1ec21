import { Dispatch } from "react";
import { <PERSON><PERSON>ota<PERSON><PERSON><PERSON>, Fi<PERSON>lertOctagon } from "react-icons/fi";

import { updateProfile } from "common/libraries/api";
import { AdminProfileReportType } from "common/components/admin/report/profile/AdminProfileReportItem";
import { adminActionLogs } from "common/libraries/adminApi";

const AdminProfileReportNSFLButton = ({
  reporteeID,
  nsfl,
  setReports,
}: {
  reporteeID: number;
  nsfl: boolean;
  setReports: Dispatch<React.SetStateAction<AdminProfileReportType[]>>;
}) => {
  const onMarkasNSFLClick = async (
    profile_id: number,
    profile_nsfl: boolean,
  ) => {
    const isConfirmed = confirm(
      profile_nsfl
        ? "Are you sure you want to mark this profile as Normal?"
        : "Are you sure you want to mark this profile as NSFL?",
    );

    if (!isConfirmed) return;

    try {
      setReports((reports) =>
        reports.map((report) => {
          if (report.reportee.id === profile_id) {
            report.reportee.nsfl = !profile_nsfl;
            report.reportee.visibility = profile_nsfl ? "public" : "hidden";
          }
          return report;
        }),
      );

      const updateResult = await updateProfile({
        updateContents: profile_nsfl
          ? { nsfl: false, visibility: "public" }
          : { nsfl: true, visibility: "hidden" },
        profileId: profile_id,
      });
      const { data: profileData = null, error: profileError = null } =
        updateResult.data;

      if (profileError) throw profileError;
    } catch (error) {
      alert("Failed to mark as NSFL :" + error);
    } finally {
      await adminActionLogs({
        target: "profiles",
        action: !profile_nsfl ? "mark_as_nsfl" : "unmark_as_nsfl",
        info: [
          {
            table: "profiles",
            id: profile_id,
            old: {
              nsfl: profile_nsfl,
            },
            new: {
              nsfl: !profile_nsfl,
            },
          },
        ],
      });
    }
  };

  return (
    <button
      className={`flex items-center gap-1 rounded border px-2 py-1 text-xs ${
        nsfl == true
          ? "bg-gray-600 text-gray-100"
          : "border border-red-600 bg-[#fcff3a] text-red-600"
      }`}
      onClick={() => {
        onMarkasNSFLClick(reporteeID, nsfl);
      }}
    >
      {nsfl ? <FiRotateCw /> : <FiAlertOctagon />}
      {nsfl == true ? "Unmark NSFL" : "Mark NSFL"}
    </button>
  );
};

export default AdminProfileReportNSFLButton;
