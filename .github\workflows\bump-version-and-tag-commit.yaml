name: Bump build version and tag commit

on:
  workflow_dispatch:
  workflow_call:
    outputs:
      committed:
        description: whether the action has created a commit ('true' or 'false')
        value: ${{ jobs.bump-version-and-tag-commit.outputs.committed }}
      commit_long_sha:
        description: the full SHA of the commit that has just been created
        value: ${{ jobs.bump-version-and-tag-commit.outputs.commit_long_sha }}
      commit_sha:
        description: the short 7-character SHA of the commit that has just been created
        value: ${{ jobs.bump-version-and-tag-commit.outputs.commit_sha }}
      pushed:
        description: whether the action has pushed to the remote ('true' or 'false')
        value: ${{ jobs.bump-version-and-tag-commit.outputs.pushed }}
      tagged:
        description: whether the action has created a tag ('true' or 'false')
        value: ${{ jobs.bump-version-and-tag-commit.outputs.tagged }}
      tag_pushed:
        description: whether the action has pushed a tag ('true' or 'false')
        value: ${{ jobs.bump-version-and-tag-commit.outputs.tag_pushed }}

concurrency:
  group: bump-${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  bump-version-and-tag-commit:
    runs-on: ubuntu-latest

    permissions:
      # Give the default GITHUB_TOKEN write permission to commit and push the changed files back to the repository.
      contents: write

    defaults:
      run:
        working-directory: ./packages/mobile

    outputs:
      committed: ${{ steps.commit-and-push.outputs.committed }}
      commit_long_sha: ${{ steps.commit-and-push.outputs.commit_long_sha }}
      commit_sha: ${{ steps.commit-and-push.outputs.commit_sha }}
      pushed: ${{ steps.commit-and-push.outputs.pushed }}
      tagged: ${{ steps.commit-and-push.outputs.tagged }}
      tag_pushed: ${{ steps.commit-and-push.outputs.tag_pushed }}

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}
      - name: Use Node.js 18
        uses: actions/setup-node@v3
        with:
          node-version: 18
          # cache: npm

      - name: Bump app version
        run: npm run set-version -- --bump

      - name: Read build version
        id: read-build-version
        run: echo "BUILD_VERSION=$(npm run --silent set-version -- --read-build-version)" >> $GITHUB_OUTPUT

      - name: output build version in a separate step
        run: echo ${{ steps.read-build-version.outputs.BUILD_VERSION }}

      - uses: EndBug/add-and-commit@v9
        id: commit-and-push
        with:
          default_author: github_actions
          message: "Bump build version to ${{ steps.read-build-version.outputs.BUILD_VERSION }}"
          tag: "build-${{ steps.read-build-version.outputs.BUILD_VERSION }} --force"
          add: |
            packages/mobile/android/app/build.gradle
            packages/mobile/ios/App/App.xcodeproj/project.pbxproj
            packages/mobile/package.json

      - name: print commit-and-push step outputs
        run: |
          echo "committed: ${{ steps.commit-and-push.outputs.committed }}"
          echo "commit_long_sha: ${{ steps.commit-and-push.outputs.commit_long_sha }}"
          echo "commit_sha: ${{ steps.commit-and-push.outputs.commit_sha }}"
          echo "pushed: ${{ steps.commit-and-push.outputs.pushed }}"
          echo "tagged: ${{ steps.commit-and-push.outputs.tagged }}"
          echo "tag_pushed: ${{ steps.commit-and-push.outputs.tag_pushed }}"
