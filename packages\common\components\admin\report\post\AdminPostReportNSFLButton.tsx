import { Dispatch } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Fi<PERSON>lertOctagon } from "react-icons/fi";

import supabase from "common/libraries/supabaseClient";
import { AdminPostReportType } from "common/components/admin/report/post/AdminPostReportItem";
import { adminActionLogs, updatePostAdmin } from "common/libraries/adminApi";
import { wrappedError } from "common/utils/errorUtils";

const AdminPostReportNSFLButton = ({
  postID,
  nsfl,
  setReports,
}: {
  postID: number;
  nsfl: boolean;
  setReports: Dispatch<React.SetStateAction<AdminPostReportType[]>>;
}) => {
  /// handle "Mark as NSFL" button click
  const handleMarkAsNSFLClick = async () => {
    /// add confirmation
    const isConfirmed = confirm(
      `Are you sure you want to mark this post as ${nsfl ? "Normal" : "NSFL"}?`,
    );

    if (!isConfirmed) return;

    try {
      const response = await updatePostAdmin({
        post_id: postID,
        contents: {
          nsfl: !nsfl,
          visibility: nsfl ? "public" : "hidden",
        },
      });
      const { data, error } = response.data;

      if (error) {
        throw wrappedError(error, "Error in handleMarkasNSFL");
      }

      setReports((reports) =>
        reports.map((report) => {
          if (report.post.id == postID) {
            report.post.nsfl = !nsfl;
          }

          return report;
        }),
      );
    } catch (error) {
      console.error(error);
    } finally {
      await adminActionLogs({
        target: "post_reports",
        action: !nsfl ? "mark_as_nsfl" : "unmark_as_nsfl",
        info: [
          {
            table: "posts",
            id: postID,
            old: {
              nsfl: nsfl,
            },
            new: {
              nsfl: !nsfl,
            },
          },
        ],
      });
    }
  };

  return (
    <button
      onClick={handleMarkAsNSFLClick}
      className={`flex w-fit items-center gap-1 rounded px-2 py-1 text-xs ${
        nsfl == true
          ? "bg-gray-600 text-white"
          : "border border-red-600 bg-[#fcff3a] text-red-600"
      }`}
    >
      {nsfl ? <FiRotateCw /> : <FiAlertOctagon />}
      {nsfl == true ? "Unmark as NSFL" : "Mark as NSFL"}
    </button>
  );
};

export default AdminPostReportNSFLButton;
