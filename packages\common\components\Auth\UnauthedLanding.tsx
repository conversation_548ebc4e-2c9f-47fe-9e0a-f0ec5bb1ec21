import { useEffect, useMemo } from "react";
import { FiPhone } from "react-icons/fi";
import { usePathname } from "next/navigation";

import GoogleSignupButton from "./GoogleSignupButton";
import logoLight from "common/<EMAIL>";
import logoDark from "common/logo_dark_mode.svg";
import AppleSignupButton from "./AppleSignupButton";
import CustomImage from "common/utils/CustomImage";
import Link from "common/utils/Link";
import { useTheme } from "common/contexts/ThemeContext";
import { isCapacitor } from "common/utils/Helper";
import AppStoreButton from "common/components/Button/AppStoreButton";
import GooglePlayButton from "common/components/Button/GooglePlayButton";
import { AUTH_EXCLUDED_URLS } from "common/config";
import { GoogleAuth } from "@codetrix-studio/capacitor-google-auth";
import { trackEvent } from "common/utils/trackEvent";
import { useOpenDesktopAuthModal } from "common/state/openDesktopAuthModalState";
import { getWebToStoreLink } from "common/utils/downloadAppLinkUtils";

export default function UnauthedLanding({
  parentLocation,
}: {
  parentLocation: "modal" | "desktop_auth_page";
}) {
  const { theme } = useTheme();
  const pathname = usePathname();

  const isAuthExcluded = useMemo(
    () => AUTH_EXCLUDED_URLS.some((uri) => new RegExp(uri).test(pathname)),
    [pathname],
  );

  const { isOpenDesktopAuthModal } = useOpenDesktopAuthModal();

  const { action, source, additionalParameters } = isOpenDesktopAuthModal ?? {};

  if (isCapacitor()) {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useEffect(() => {
      GoogleAuth.initialize();
    }, []);
  }

  const fullscreen = parentLocation === "desktop_auth_page";

  const appStoreLink = useMemo(() => {
    return getWebToStoreLink({
      link_location: parentLocation,
      store_type: "app_store",
      additionalParams: { action, source, ...(additionalParameters ?? {}) },
    });
  }, [action, additionalParameters, parentLocation, source]);

  // const playStoreLink = useMemo(() => {
  //   return getWebToStoreLink({
  //     link_location: parentLocation,
  //     store_type: "play_store",
  //     additionalParams: { action, source, ...(additionalParameters ?? {}) },
  //   });
  // }, [action, additionalParameters, parentLocation, source]);

  return (
    <div
      className={`flex ${
        fullscreen ? "min-h-screen" : "rounded-2xl"
      } items-center justify-center bg-white px-4 py-12 text-black dark:bg-black-100 dark:text-white sm:px-6 lg:px-8`}
    >
      <div className="w-full max-w-md space-y-8">
        <CustomImage
          src={
            isCapacitor()
              ? theme == "dark"
                ? logoDark
                : logoLight
              : theme == "dark"
                ? logoDark.src
                : logoLight.src
          }
          alt="Logo"
          height={40}
          width={40}
        />

        <div className="text-left">
          <h2 className="mt-6 text-left text-3xl font-extrabold text-gray-900 dark:text-white">
            Butterflies
          </h2>
          <p className="text-md mt-2 text-left text-gray-600 dark:text-white-500">
            Login or sign up with...
          </p>
        </div>
        <div className="2-space-y-px rounded-md shadow-sm">
          <div>
            <Link
              onClick={() => {
                trackEvent("auth.provider.button_pressed", {
                  provider: "phone",
                });
              }}
              href={`/users/signin/phone${
                isAuthExcluded ? "" : "?returnUrl=" + pathname
              }`}
            >
              <button
                type="button"
                className="group relative mb-4 flex w-full items-center justify-center rounded-md border border-transparent bg-black px-4 py-3 text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:bg-white-100 dark:text-black-100"
              >
                <FiPhone size={18} className="mr-3" />
                Phone Number
              </button>
            </Link>
          </div>

          <div className="mb-4">
            <AppleSignupButton />
          </div>
          <div>
            <GoogleSignupButton />
          </div>
          <div className="my-2 flex items-center justify-center">
            <div className="w-1/3 border-t border-gray-300"></div>
            <span className="mx-4 text-gray-500 dark:text-white-500">OR</span>
            <div className="w-1/3 border-t border-gray-300"></div>
          </div>
          <div>
            <Link
              onClick={() => {
                trackEvent("auth.provider.button_pressed", {
                  provider: "email",
                  signin: false,
                });
              }}
              href={`/users/signup/email`}
              routerDirection="forward"
            >
              <button
                type="button"
                className="group relative flex w-full justify-center rounded-md border border-transparent bg-gray-500 px-4 py-3 text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:bg-white-100 dark:text-black-100"
              >
                Email and Password
              </button>
            </Link>
          </div>
        </div>
        <div className="mt-6 text-center text-sm text-gray-500 dark:text-white">
          Already have an account?
          <Link
            onClick={() => {
              trackEvent("auth.provider.button_pressed", {
                provider: "email",
                signin: true,
              });
            }}
            href={`/users/signin/email${
              isAuthExcluded ? "" : "?returnUrl=" + pathname
            }`}
            routerDirection="forward"
          >
            <button
              type="button"
              className="ml-2 rounded-md border border-gray-300 px-4 py-1 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:hover:bg-black-100"
            >
              Sign in
            </button>
          </Link>
        </div>

        <div className="w-full">
          <div className="flex items-center justify-center px-20">
            <AppStoreButton href={appStoreLink} />
          </div>
        </div>
      </div>
    </div>
  );
}
