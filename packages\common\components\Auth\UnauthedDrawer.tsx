"use client";
import { useContext, useEffect, useState } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import Sheet from "react-modal-sheet";

import { UserContext } from "../../contexts/UserContext";
import { useTheme } from "../../contexts/ThemeContext";
import { IonList, useIonViewWillEnter } from "@ionic/react";
import { isCapacitor } from "../../utils/Helper";
import { useOpenDesktopAuthModal } from "common/state/openDesktopAuthModalState";
import { useActionRequiresAuth } from "common/state/useActionRequiresAuth";
import { useGlobalRouter } from "common/utils/useGlobalRouter";

const UnauthedDrawer = () => {
  const [close, setClose] = useState(false);
  const [sheetClose, setSheetClose] = useState(false);
  const { selectedProfile, user }: any = useContext(UserContext);

  const { onActionRequiresAuth } = useActionRequiresAuth();
  const { setIsOpenDesktopAuthModal } = useOpenDesktopAuthModal();

  const { theme } = useTheme();

  const router = useGlobalRouter();

  useEffect(() => {
    return () => {
      setIsOpenDesktopAuthModal(undefined);
    };
  }, []);

  useIonViewWillEnter(() => {
    setSheetClose(false);
  });

  return (
    <>
      {isCapacitor() && !user && !selectedProfile ? (
        <Sheet
          isOpen={!sheetClose}
          onClose={() => {
            setSheetClose(!sheetClose);
          }}
          detent="content-height"
          disableScrollLocking={true}
        >
          <Sheet.Backdrop onClick={() => setSheetClose(!sheetClose)} />
          <Sheet.Container
            style={{
              backgroundColor: theme == "dark" ? "var(--modal-color)" : "white",
            }}
          >
            <Sheet.Header />
            <Sheet.Content>
              <IonList
                className="mb-4 dark:bg-darkgray"
                style={{
                  "--background":
                    theme == "light" ? "white" : "var(--modal-color)",
                }}
              >
                <div className="text-md px-4 pb-2.5 text-center font-semibold dark:text-white sm:text-start">
                  {/* Sign up to create AI characters. */}
                  Download the app to create AI characters.
                </div>
                <div className="flex h-1/2 w-full flex-row justify-center">
                  <button
                    className="mx-8 w-full gap-2 rounded-lg bg-black-100 px-4 py-2 text-base font-normal text-white dark:bg-white dark:text-black-100"
                    onClick={() => {
                      router.push("/auth/start");
                    }}
                  >
                    {/* Sign Up for Free */}
                    Download the App
                  </button>
                </div>
              </IonList>
            </Sheet.Content>
          </Sheet.Container>
          <Sheet.Backdrop />
        </Sheet>
      ) : (
        <>
          {user == null &&
            selectedProfile == undefined &&
            (!close ? (
              <section
                id="drawer-bottom-example"
                className="fixed bottom-0 left-0 right-0 z-40 mx-auto transform-none overflow-y-auto bg-white p-4 shadow-[0_-5px_80px_-25px_rgba(0,0,0,0.3)] transition-transform dark:border-t dark:border-t-white dark:bg-black-100 sm:top-auto sm:p-8"
                aria-labelledby="drawer-bottom-label"
              >
                <div className="flex flex-col place-items-center justify-center gap-4 sm:mx-20 sm:flex-row">
                  <div className="flex w-full flex-col place-items-center justify-center gap-2 sm:mx-auto sm:flex-row">
                    <div className="flex flex-col gap-4">
                      <div className="text-center text-lg font-semibold dark:text-gray-100 sm:text-start">
                        Download Butterflies to create AI characters for Free.
                      </div>
                    </div>
                    <div className="flex w-full flex-row justify-end sm:w-1/3">
                      <button
                        className="mx-4 w-full gap-2 rounded-lg bg-black-100 px-4 py-2 text-base font-normal text-white dark:border dark:border-white sm:w-1/2"
                        onClick={() =>
                          onActionRequiresAuth({
                            action: "get_app_tapped",
                            source: "unauthed_bottom_drawer",
                            linkPath: undefined,
                          })
                        }
                      >
                        Get the App
                      </button>
                    </div>
                  </div>
                  <button
                    type="button"
                    data-drawer-hide="drawer-bottom-example"
                    aria-controls="drawer-bottom-example"
                    onClick={() => {
                      setClose(true);
                    }}
                    className="absolute end-2 top-2 inline-flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white"
                  >
                    <XMarkIcon className="h-8 w-8" />
                    <span className="sr-only">Close menu</span>
                  </button>
                </div>
              </section>
            ) : (
              ""
            ))}
        </>
      )}
    </>
  );
};

export default UnauthedDrawer;
