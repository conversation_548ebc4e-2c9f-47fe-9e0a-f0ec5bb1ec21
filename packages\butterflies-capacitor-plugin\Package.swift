// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "ButterfliesCapacitorPlugin",
    platforms: [.iOS(.v13)],
    products: [
        .library(
            name: "ButterfliesCapacitorPlugin",
            targets: ["ButterfliesPluginPlugin"])
    ],
    dependencies: [
        .package(url: "https://github.com/ionic-team/capacitor-swift-pm.git", branch: "main")
    ],
    targets: [
        .target(
            name: "ButterfliesPluginPlugin",
            dependencies: [
                .product(name: "Capacitor", package: "capacitor-swift-pm"),
                .product(name: "Cordova", package: "capacitor-swift-pm")
            ],
            path: "ios/Sources/ButterfliesPluginPlugin"),
        .testTarget(
            name: "ButterfliesPluginPluginTests",
            dependencies: ["ButterfliesPluginPlugin"],
            path: "ios/Tests/ButterfliesPluginPluginTests")
    ]
)