import { DEFAULT_AVATAR_URL } from "common/config/constants";

const AdminProfileReportLoading = () => {
  return (
    <div className="mx-auto flex w-full max-w-full flex-col justify-start bg-white text-xs dark:bg-black-100">
      <div className="mt-3 w-full space-y-4 overflow-hidden rounded-lg border border-gray-200 bg-white text-left text-base font-medium text-gray-900 shadow-sm hover:shadow-lg focus-visible:ring focus-visible:ring-gray-500/75 dark:border-gray-500 dark:bg-slate-900 dark:text-white">
        <div className="flex w-full flex-col lg:flex-row">
          <div className="flex w-full flex-row sm:w-2/3">
            <div className="hidden h-[236px] w-56 items-center object-cover sm:block">
              <svg
                className="-ml-3 h-auto w-auto text-gray-200 dark:text-gray-600"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="currentColor"
                viewBox="0 0 18 20"
              >
                <path d="M18 0H2a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2Zm-5.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm4.376 10.481A1 1 0 0 1 16 15H4a1 1 0 0 1-.895-1.447l3.5-7A1 1 0 0 1 7.468 6a.965.965 0 0 1 .9.5l2.775 4.757 1.546-1.887a1 1 0 0 1 1.618.1l2.541 4a1 1 0 0 1 .028 1.011Z" />
              </svg>
            </div>

            <div className="flex flex-1 flex-col justify-between border-r">
              <div className="flex h-full w-full flex-col p-3">
                <div className="flex flex-row justify-between gap-4">
                  <div className="flex h-full w-full flex-col gap-4">
                    <div className="flex h-4 w-40 animate-pulse gap-1 rounded-full bg-gray-300" />
                    <div className="flex h-4 w-full animate-pulse gap-1 rounded-full bg-gray-300" />
                  </div>

                  <div className="flex h-fit w-fit min-w-[20rem] items-center gap-2 overflow-hidden rounded-l-[2.75rem] rounded-r-xl border p-1 pr-4">
                    <img
                      src={DEFAULT_AVATAR_URL}
                      alt={"Poster avatar"}
                      className="h-20 w-20"
                    />
                    <div className="z-10 flex flex-col gap-3">
                      <div className="flex h-4 w-[80px] animate-pulse gap-1 rounded-full bg-gray-300" />
                      <div className="flex h-4 w-[100px] animate-pulse gap-1 rounded-full bg-gray-300" />
                      <div className="flex h-4 w-40 animate-pulse gap-1 rounded-full bg-gray-300" />
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col items-end justify-between gap-2 p-3 lg:flex-row">
                <div className="flex flex-col items-start gap-2 lg:flex-row">
                  <div className="flex h-4 w-16 animate-pulse gap-1 rounded-full bg-gray-300" />
                  <div className="flex h-4 w-16 animate-pulse gap-1 rounded-full bg-gray-300" />
                  <div className="flex h-4 w-16 animate-pulse gap-1 rounded-full bg-gray-300" />
                </div>

                <div className="flex flex-col items-end gap-2 lg:flex-row">
                  <div className="flex h-4 w-20 animate-pulse gap-1 rounded-full bg-gray-300" />
                  <div className="flex h-4 w-20 animate-pulse gap-1 rounded-full bg-gray-300" />
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col justify-between gap-2 p-3 sm:w-1/3">
            <div className="flex flex-col gap-2">
              <div className="relative flex h-fit w-fit min-w-[20rem] items-center gap-2 overflow-hidden rounded-l-[2.75rem] rounded-r-xl border p-1 pr-4">
                <img
                  src={DEFAULT_AVATAR_URL}
                  alt={"Reporter avatar"}
                  className="h-20 w-20"
                />

                <div className="flex flex-col gap-3">
                  <div className="flex h-4 w-20 animate-pulse gap-1 rounded-full bg-gray-300" />
                  <div className="flex h-4 w-[100px] animate-pulse gap-1 rounded-full bg-gray-300" />
                  <div className="flex h-4 w-[160px] animate-pulse gap-1 rounded-full bg-gray-300" />
                </div>
              </div>

              <div className="flex h-4 w-16 animate-pulse gap-1 rounded-full bg-gray-300" />
              <div className="flex max-w-full flex-wrap gap-2">
                <div className="flex h-4 w-16 animate-pulse gap-1 rounded-full bg-gray-300" />
                <div className="flex h-4 w-16 animate-pulse gap-1 rounded-full bg-gray-300" />
                <div className="flex h-4 w-16 animate-pulse gap-1 rounded-full bg-gray-300" />
              </div>
            </div>

            <div className="flex w-full justify-end">
              <div className="flex h-4 w-20 animate-pulse gap-1 rounded-full bg-gray-300" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminProfileReportLoading;
