import { Dispatch } from "react";
import { Fi<PERSON>lag, FiRotateCw } from "react-icons/fi";

import { updateProfile } from "common/libraries/api";
import { AdminProfileReportType } from "common/components/admin/report/profile/AdminProfileReportItem";
import { adminActionLogs } from "common/libraries/adminApi";

const AdminProfileReportNSFWButton = ({
  reporteeID,
  nsfw,
  setReports,
}: {
  reporteeID: number;
  nsfw: string;
  setReports: Dispatch<React.SetStateAction<AdminProfileReportType[]>>;
}) => {
  const onMarkasNSFWClick = async (
    profile_id: number,
    profile_nsfw: string,
  ) => {
    const isConfirmed = confirm(
      profile_nsfw == "nsfw"
        ? "Are you sure you want to mark this profile as Normal?"
        : "Are you sure you want to mark this profile as NSFW?",
    );

    if (!isConfirmed) return;

    try {
      setReports((reports) =>
        reports.map((report) => {
          if (report.reportee.id === profile_id) {
            report.reportee.nsfw = profile_nsfw == "nsfw" ? "normal" : "nsfw";
          }
          return report;
        }),
      );

      const updateResult = await updateProfile({
        updateContents:
          profile_nsfw == "nsfw" ? { nsfw: "normal" } : { nsfw: "nsfw" },
        profileId: profile_id,
      });
      const { data: profileData = null, error: profileError = null } =
        updateResult.data;

      if (profileError) throw profileError;
    } catch (error) {
      setReports((prevReports) => [...prevReports]);

      alert("Failed to mark as NSFW :" + error);
    } finally {
      await adminActionLogs({
        target: "profiles",
        action: profile_nsfw === "normal" ? "mark_as_nsfw" : "unmark_as_nsfw",
        info: [
          {
            table: "profiles",
            id: profile_id,
            old: {
              nsfw: profile_nsfw == "nsfw" ? "nsfw" : "normal",
            },
            new: {
              nsfw: profile_nsfw == "nsfw" ? "normal" : "nsfw",
            },
          },
        ],
      });
    }
  };

  return (
    <button
      className={`flex items-center gap-1 rounded border px-2 py-1 text-xs ${
        nsfw == "nsfw"
          ? "border-green-600 bg-green-600 text-green-100"
          : "border-red-600 bg-red-600 text-red-100"
      }`}
      onClick={() => {
        onMarkasNSFWClick(reporteeID, nsfw);
      }}
    >
      {nsfw == "nsfw" ? <FiRotateCw /> : <FiFlag />}
      {nsfw == "nsfw" ? "Unmark NSFW" : "Mark NSFW"}
    </button>
  );
};

export default AdminProfileReportNSFWButton;
