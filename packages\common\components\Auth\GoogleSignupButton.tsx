"use client";
import { usePathname, useSearchParams } from "next/navigation";
import { Dispatch, useContext, useMemo, useState } from "react";

import { AUTH_EXCLUDED_URLS, MOBILE_SITE_URL, SITE_URL } from "common/config";
import { UserContext } from "common/contexts/UserContext";
import supabase from "common/libraries/supabaseClient";
import { isCapacitor } from "common/utils/Helper";
import Loading from "../Loading";

import { GoogleAuth } from "@codetrix-studio/capacitor-google-auth";
import CustomButtonV2 from "../../utils/CustomButtonV2";
import { useFeatureFlagEnabled } from "common/utils/posthog";
import { useToast } from "common/contexts/ToastContext";
import { setItemStorage } from "common/utils/localStorageWrappers";
import { getRuntimeEnvironment } from "common/utils/RuntimeEnvironment";
import { useSet<PERSON><PERSON> } from "jotai";
import { nameFromAuthProvider<PERSON>tom } from "common/state/userInfoState";
import { useTheme } from "common/contexts/ThemeContext";
import ImgElement from "common/utils/ImgElement";
import { Icons } from "common/assets/IconLoader";
import { trackEvent } from "common/utils/trackEvent";
import { captureError } from "common/utils/sentryWrapper";

const signInUsingSupabaseGoogle = async () => {
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: "google",
    options: {
      redirectTo: `${
        isCapacitor()
          ? `${MOBILE_SITE_URL}/auth/callback`
          : `${SITE_URL}/auth/callback`
      }`,

      queryParams: {
        access_type: "offline",
        prompt: "consent",
        mobile: "1",
      },
    },
  });

  if (error) {
    console.error(
      "Error signing in with Google using supabase client:",
      error.message,
    );
    throw error;
  }

  return data;
};

const signInUsingCapacitorGoogle = async (
  setNameFromAuthProvider: Dispatch<string | undefined>,
) => {
  let googleAuthData;
  try {
    googleAuthData = await GoogleAuth.signIn();
    // We choose to use just the given name instead of the full name,
    // because it's more natural for butterlies to use that when chatting
    // with the user.
    const name = googleAuthData?.givenName ?? googleAuthData?.name;
    setNameFromAuthProvider(name || undefined);
  } catch (error: any) {
    console.error(
      "Error signing in with Google using capacitor plugin:",
      error.message,
    );
    throw error;
  }

  const { data, error } = await supabase.auth.signInWithIdToken({
    provider: "google",
    token: googleAuthData?.authentication.idToken,
    access_token: googleAuthData?.authentication.accessToken,
  });

  if (error) {
    console.error(
      "Error passing Google auth code to supabase client:",
      error.message,
    );
    throw error;
  }

  return data;
};

export default function GoogleSignupButton({
  signup = false,
  isNewStyle = false,
}) {
  const {
    loadUser,
    loadUserData,
    isAuthWaitingForNavigation,
    setIsAuthWaitingForNavigation,
  } = useContext(UserContext);

  const setNameFromAuthProvider = useSetAtom(nameFromAuthProviderAtom);
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const showLoading = isLoading || isAuthWaitingForNavigation === "google";
  const { theme } = useTheme();

  const forceDisableNativeGoogleSignin = useFeatureFlagEnabled(
    "FORCE_DISABLE_NATIVE_GOOGLE_SIGNIN",
  );

  const isNative = getRuntimeEnvironment()?.isNative() === true;

  const return_url = searchParams?.get("returnUrl");

  if (return_url) {
    setItemStorage("returnUrl", return_url);
  }

  const isAuthExcluded = useMemo(
    () => AUTH_EXCLUDED_URLS.some((uri) => new RegExp(uri).test(pathname)),
    [pathname],
  );

  const { invokeToast } = useToast();

  const handleSignup = async () => {
    trackEvent("auth.provider.button_pressed", {
      provider: "google",
    });

    setIsLoading(true);
    if (!isAuthExcluded) {
      setItemStorage("returnUrl", pathname);
    }

    let canUseNativePlugin;

    try {
      let data:
        | Awaited<ReturnType<typeof signInUsingSupabaseGoogle>>
        | Awaited<ReturnType<typeof signInUsingCapacitorGoogle>>;

      canUseNativePlugin = isNative && !forceDisableNativeGoogleSignin;

      trackEvent("auth.provider.started", {
        provider: "google",
        nativePlugin: canUseNativePlugin,
      });

      if (canUseNativePlugin) {
        data = await signInUsingCapacitorGoogle(setNameFromAuthProvider);
        if (data) {
          ///////////////////////////////
          // TODO: we have a few places where we do something like this.
          // TODO: need a better way to orchestrate post-auth logic and navigation
          setIsAuthWaitingForNavigation("google");
          loadUser();
          loadUserData(data?.user);
          ///////////////////////////////
        }
        trackEvent("auth.provider.completed", {
          provider: "google",
          nativePlugin: canUseNativePlugin,
        });
      } else {
        data = await signInUsingSupabaseGoogle();
      }
    } catch (error: any) {
      trackEvent("auth.provider.failed", {
        provider: "google",
        nativePlugin: canUseNativePlugin,
        error,
      });

      if (typeof error.message !== "undefined") {
        if (
          typeof error.message === "string" &&
          error.message.includes("user canceled")
        ) {
          // user cancelled
        } else {
          invokeToast({
            type: "error",
            text: "Failed to signing in with google.",
            position: "top",
          });
          captureError(error, "Failed to signup with Google");
        }
      }
      console.error("Error signing in with Google:", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {isNewStyle ? (
        <CustomButtonV2
          type="secondary"
          onClick={handleSignup}
          disabled={showLoading}
        >
          <ImgElement
            className="absolute left-0 h-6 w-6"
            src={theme === "light" ? Icons.GoogleIcon : Icons.GoogleDarkIcon}
          />
          Continue with Google
          {showLoading && (
            <Loading size={3} style={{ marginLeft: "6px", color: "black" }} />
          )}
        </CustomButtonV2>
      ) : (
        <button
          type="button"
          className="mb-2 mr-2 inline-flex h-11 w-full items-center justify-center rounded-lg bg-[#4285F4] px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-[#4285F4]/90 focus:ring-4 focus:ring-[#4285F4]/50 dark:focus:ring-[#4285F4]/55"
          onClick={handleSignup}
          disabled={showLoading}
        >
          <svg
            className="-ml-1 mr-2 h-4 w-4"
            aria-hidden="true"
            focusable="false"
            data-prefix="fab"
            data-icon="google"
            role="img"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 488 512"
          >
            <path
              fill="currentColor"
              d="M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"
            ></path>
          </svg>
          {signup ? "Sign up" : "Sign in"} with Google{" "}
          {showLoading && <Loading size={3} style={{ marginLeft: "6px" }} />}
        </button>
      )}
    </>
  );
}
