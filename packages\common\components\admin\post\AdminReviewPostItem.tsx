"use client";
import React, { Dispatch } from "react";
import dayjs from "dayjs";
import {
  FiEdit,
  <PERSON>Flag,
  FiAlertOctagon,
  FiRotateCw,
  FiCheckCircle,
} from "react-icons/fi";
import Link from "next/link";
import { useAtomValue } from "jotai";

import ResponsiveImage from "common/components/ResponsiveImage";
import AdminProfileMiniItem from "common/components/admin/profile/AdminProfileMiniItem";
import { selectedProfileAtom } from "common/state/selectedProfile";
import {
  adminActionLogs,
  adminReivews,
  updatePostAdmin,
} from "common/libraries/adminApi";
import { wrappedError } from "common/utils/errorUtils";

export interface Post {
  id: number;
  mediaUrl: string | null;
  nsfw: string;
  nsfwScore?: number;
  nsfl: boolean;
  reviewId?: number;
  profileAge?: string;
  visibility: string;
  userId?: string;
  profiles?: {
    userId?: string;
  };
  imitation: string;
  description: string;
  profileId: number;
  displayName: string;
  username: string;
  profileNsfw: string;
  profileNsfl: boolean;
  createdAt: string;
  tags?: string;
}

interface AdminReviewPostItemProps {
  post: Post;
  setPosts: Dispatch<React.SetStateAction<Post[]>>;
}

const AdminReviewPostItem = ({ post, setPosts }: AdminReviewPostItemProps) => {
  const selectedProfile = useAtomValue(selectedProfileAtom);

  const handleReviewPost = async (postId: number): Promise<void> => {
    if (!postId || !selectedProfile?.id) {
      console.error("Invalid post ID or reviewer ID");
      return;
    }

    const userConfirmed = window.confirm(
      "Are you sure you want to mark this post as reviewed?",
    );
    if (!userConfirmed) {
      console.log(`Review cancelled for post ${postId}`);
      return;
    }

    try {
      // Update local state optimistically
      setPosts((prevPosts) => prevPosts.filter((post) => post.id !== postId));

      // Make API call to mark post as reviewed
      const { data, error } = (
        await adminReivews("post_reviews", {
          post_id: postId,
          reviewer_id: selectedProfile.id,
        })
      ).data;

      if (error) {
        throw wrappedError(error, "Failed to review post");
      }

      // Log admin action
      await adminActionLogs({
        target: "posts",
        action: "mark_as_reviewed",
        info: [{ table: "post_reviews", id: postId }],
      });

      console.log(`Post ${postId} successfully reviewed`);
    } catch (error) {
      // Revert optimistic update on error
      console.error("Review post error:", error);
      alert(
        `Failed to review post: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  };

  const handleMarkAsNSFW = async (
    postId: number,
    currentNsfwStatus: string,
  ): Promise<void> => {
    const newNsfwStatus = currentNsfwStatus === "nsfw" ? "normal" : "nsfw";
    const actionType =
      currentNsfwStatus === "normal" ? "mark_as_nsfw" : "unmark_as_nsfw";

    try {
      // Optimistic update
      setPosts((prevPosts) =>
        prevPosts.map((post) =>
          post.id === postId ? { ...post, nsfw: newNsfwStatus } : post,
        ),
      );

      // API call
      const { data, error } = (
        await updatePostAdmin({
          post_id: postId,
          contents: { nsfw: newNsfwStatus },
        })
      ).data;

      if (error) {
        throw wrappedError(error, "Error updating NSFW status");
      }

      // Log action
      await adminActionLogs({
        target: "posts",
        action: actionType,
        info: [
          {
            table: "posts",
            id: postId,
            old: { nsfw: currentNsfwStatus },
            new: { nsfw: newNsfwStatus },
          },
        ],
      });

      console.log(`Post ${postId} ${actionType} successfully`);
    } catch (error) {
      // Revert optimistic update on error
      setPosts((prevPosts) =>
        prevPosts.map((post) =>
          post.id === postId ? { ...post, nsfw: currentNsfwStatus } : post,
        ),
      );
      alert(
        `Failed to update NSFW status: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  };

  const handleMarkasNSFL = async (
    postId: number,
    postNsfl: boolean,
  ): Promise<void> => {
    const newNsflStatus = !postNsfl;
    const actionType = newNsflStatus ? "mark_as_nsfl" : "unmark_as_nsfl";
    const confirmMessage = `Are you sure you want to mark this post as ${newNsflStatus ? "NSFL" : "Normal"}?`;

    if (!window.confirm(confirmMessage)) {
      console.log(`NSFL update cancelled for post ${postId}`);
      return;
    }

    try {
      // Optimistic update
      setPosts((prevPosts) =>
        prevPosts.map((post) =>
          post.id === postId
            ? {
                ...post,
                nsfl: newNsflStatus,
                visibility: newNsflStatus ? "hidden" : "public",
              }
            : post,
        ),
      );

      // API call
      const { data, error } = (
        await updatePostAdmin({
          post_id: postId,
          contents: {
            nsfl: newNsflStatus,
            visibility: newNsflStatus ? "hidden" : "public",
          },
        })
      ).data;

      if (error) {
        throw wrappedError(error, "Failed to update NSFL status");
      }

      // Log admin action
      await adminActionLogs({
        target: "posts",
        action: actionType,
        info: [
          {
            table: "posts",
            id: postId,
            old: { nsfl: postNsfl },
            new: { nsfl: newNsflStatus },
          },
        ],
      });

      console.log(`Post ${postId} ${actionType} successfully`);
    } catch (error) {
      // Revert optimistic update on error
      setPosts((prevPosts) =>
        prevPosts.map((post) =>
          post.id === postId
            ? {
                ...post,
                nsfl: postNsfl,
                visibility: postNsfl ? "hidden" : "public",
              }
            : post,
        ),
      );

      alert(
        `Failed to update NSFL status: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  };

  return (
    <div className="focus:shadow-outline relative flex w-full flex-col overflow-hidden rounded-lg border border-gray-200 text-base shadow-sm hover:shadow-lg focus-visible:ring focus-visible:ring-gray-500/75 dark:border-gray-500 dark:bg-slate-900 dark:text-white xl:flex-row">
      <Link
        href={post.mediaUrl ?? "#"}
        target="_blank"
        className="h-[200px] w-[160px] items-center overflow-hidden border-b border-r bg-blue-50 object-cover dark:bg-slate-800"
      >
        {post.mediaUrl && (
          <ResponsiveImage
            src={post.mediaUrl}
            alt="Post"
            size={[160, 200]}
            autoHeight
            cover
          />
        )}
      </Link>

      <div className="flex h-auto flex-1 flex-col justify-between gap-2 p-2">
        <div className="flex justify-between gap-8">
          <div className="space-y-0.5">
            {/* Post ID */}
            <div className="flex items-center gap-2">
              <div className="flex gap-1">
                <div>Post ID:</div>
                <Link
                  className="link"
                  href="/admin/posts/[id]/edit"
                  as={`/admin/posts/${post.id}/edit`}
                >
                  {post.id}
                </Link>
              </div>

              {/* NSFW */}
              {post.nsfw === "nsfw" && (
                <div className="flex items-center overflow-hidden rounded-full border border-red-400">
                  <div className="rounded-l-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-gray-700 dark:text-red-400">
                    NSFW
                  </div>
                  {post?.nsfwScore && (
                    <div className="rounded-xl bg-white px-2.5 py-0.5 text-xs font-medium text-[#ff4747] dark:bg-gray-700 dark:text-gray-600">
                      {post.nsfwScore.toFixed(2)}
                    </div>
                  )}
                </div>
              )}

              {/* NSFL */}
              {post.nsfl == true && (
                <div className="rounded-xl border border-[#ff4747] bg-[#fcff3a] px-2.5 py-0.5 text-xs font-medium text-[#ff4747] dark:bg-gray-700 dark:text-red-500">
                  NSFL
                </div>
              )}

              {post.reviewId && (
                <div className="rounded-xl border border-lime-400 bg-lime-100 px-2.5 py-0.5 text-xs font-medium text-lime-800 dark:bg-gray-700 dark:text-lime-400">
                  Reviewed
                </div>
              )}

              {!!post?.profileAge && (
                <div
                  className={`rounded-xl border ${
                    post?.profileAge == "adult"
                      ? `border-red-400 bg-red-100 text-red-800 dark:text-red-400`
                      : `border-green-400 bg-green-100 text-green-800 dark:text-green-400`
                  } px-2.5 py-0.5 text-xs font-medium dark:bg-gray-700`}
                >
                  {post.profileAge.charAt(0).toUpperCase() +
                    post.profileAge.slice(1)}
                </div>
              )}

              {post.visibility !== "public" && (
                <div
                  className={
                    post.visibility === "public"
                      ? "rounded-xl border border-green-800 bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-300"
                      : post.visibility === "private"
                        ? "rounded-xl border border-pink-800 bg-pink-100 px-2.5 py-0.5 text-xs font-medium text-pink-800 dark:bg-pink-900 dark:text-pink-300"
                        : "rounded-xl border border-gray-800 bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                  }
                >
                  <div className="">
                    {post.visibility.charAt(0).toUpperCase() +
                      post.visibility.slice(1)}
                  </div>
                </div>
              )}

              {(post.userId ?? post?.profiles?.userId) != null && (
                <div className="rounded-xl border border-sky-400 bg-sky-100 px-2.5 py-0.5 text-xs font-medium text-sky-800 dark:bg-gray-700 dark:text-sky-400">
                  Human
                </div>
              )}
              {post.imitation !== "normal" && (
                <div className="rounded-xl border border-violet-400 bg-violet-100 px-2.5 py-0.5 text-xs font-medium text-violet-800 dark:bg-gray-700 dark:text-violet-400">
                  {post.imitation}
                </div>
              )}
            </div>

            {/* Description */}
            <p>
              {"Description: "}
              <span className="text-gray-500">{post.description}</span>
            </p>
          </div>

          <div className="flex flex-col items-end gap-2">
            <AdminProfileMiniItem
              id={post.profileId}
              avatarURL={null}
              name={post.displayName}
              username={post.username}
              nsfw={post.profileNsfw}
              nsfl={post.profileNsfl}
              bgColor={"blue"}
            />
            <p>{dayjs(post.createdAt).fromNow()}</p>
          </div>
        </div>

        <div className="flex w-full flex-row items-end justify-between gap-3 sm:flex-row">
          <div className="flex flex-col items-start gap-2 lg:flex-row">
            {post.tags?.split(",").map((tag: string, index: number) => (
              <div
                className="rounded bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:bg-gray-900 dark:text-gray-300"
                key={index}
              >
                {tag}
              </div>
            ))}
          </div>

          <div className="flex flex-col items-end justify-center gap-2 lg:flex-row">
            <div className="flex flex-col items-end gap-2 lg:flex-row">
              <button
                className={`flex w-fit items-center gap-1 rounded px-2 py-1 text-xs ${
                  post.nsfl == true
                    ? "bg-gray-600 text-white"
                    : "border border-red-600 bg-[#fcff3a] text-red-600"
                }`}
                onClick={() => {
                  handleMarkasNSFL(post.id, post.nsfl);
                }}
              >
                {post.nsfl ? <FiRotateCw /> : <FiAlertOctagon />}
                {post.nsfl == true ? "Unmark as NSFL" : "Mark as NSFL"}
              </button>
              <button
                className={`flex w-fit items-center gap-1 rounded ${
                  post.nsfw == "nsfw" ? "bg-green-600" : "bg-pink-600"
                } px-2 py-1 text-xs text-white`}
                onClick={() => {
                  handleMarkAsNSFW(post.id, post.nsfw);
                }}
              >
                <FiFlag />
                {post.nsfw == "nsfw" ? "Unmark as NSFW" : "Mark as NSFW"}
              </button>
            </div>
            {selectedProfile?.users?.role == "admin" && (
              <Link href={`/admin/posts/${post.id}/edit`}>
                <button className="flex items-center gap-1 rounded bg-blue-600 px-2 py-1 text-xs text-white">
                  <FiEdit /> Edit
                </button>
              </Link>
            )}

            <button
              className={`flex items-center gap-1 rounded bg-lime-600 px-2 py-1 text-xs text-white`}
              onClick={() => handleReviewPost(post?.id)}
            >
              <FiCheckCircle />
              Done
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminReviewPostItem;
