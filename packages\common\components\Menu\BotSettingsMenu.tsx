import { useContext, useState } from "react";
import { useHistory } from "react-router";
import {
  FiTrash2,
  <PERSON><PERSON><PERSON>,
  <PERSON>Edit,
  FiPause,
  FiPlay,
  FiMessageSquare,
  FiHeart,
  FiAlertCircle,
} from "react-icons/fi";
import { useIonAlert } from "@ionic/react";
import { TbUserExclamation, TbUserQuestion } from "react-icons/tb";
import { MdOutlineVisibility, MdVoiceChat } from "react-icons/md";

import { deleteProfile, botState } from "common/libraries/api";
import MenuRow from "common/components/Menu/MenuRow";
import { isCapacitor } from "common/utils/Helper";
import Loading from "../Loading";
import { BotsContext } from "common/contexts/BotsContext";
import { useNextRouting } from "common/contexts/NextRoutingContext";
import ModalBox from "../Modal/ModalBox";
import { useBotFields } from "common/contexts/BotFieldsContext";
import { isFeatureFlagEnabled } from "common/utils/posthog";
import { trackEvent } from "common/utils/trackEvent";
import { setItemStorage } from "common/utils/localStorageWrappers";

export default function BotSettingsMenu({
  bot,
  setBot,
  setIsEditButterflyModalOpen,
  setIsVisibilityModalOpen,
  setIsAttributionModalOpen,
  setIsEditAppearanceModalOpen,
  setIsEditPersonalityModalOpen,
  setIsEditCommentModalOpen,
  setIsVoiceModalOpen,
}) {
  const { fetchBots } = useContext(BotsContext);
  const { botFields, setBotFields } = useBotFields();
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [isBotDeleteModal, setIsBotDeleteModal] = useState(false);
  const [presentAlert] = useIonAlert();
  let router;

  if (isCapacitor()) {
    router = useHistory();
  } else {
    router = useNextRouting();
  }

  const toggleStartStopBot = async () => {
    try {
      const result = await botState(bot?.id, !bot.is_active);
      const { error } = result.data;
      if (error) throw error;
      setBot({ ...bot, is_active: !bot.is_active });
    } catch (error) {
      console.error(error);
    }
  };

  const deleteModalContents = () => {
    return (
      <div className="flex flex-col items-center text-center">
        <div id="modal_body" className="flex flex-col items-center gap-6 py-6">
          <div
            id="icon"
            className="flex h-14 w-14 items-center justify-center rounded-full bg-[#fef3f2]"
          >
            <div className="flex h-9 w-9 items-center justify-center rounded-full bg-[#fee4e2]">
              <FiAlertCircle className="h-6 w-6 text-[#d92d20]" />
            </div>
          </div>
          <p className="text-xl font-semibold text-gray-700 dark:text-[#a3b0ca]">
            Delete Your Bot
          </p>
          <p className="w-3/4 text-center text-gray-600 dark:text-[#727d96]">
            Are you sure you want to delete this bot? This action will delete
            your bot permanently.
          </p>
        </div>
        <footer className="flex w-1/2 justify-between gap-8 py-6 text-center">
          <button
            className="flex h-10 w-full min-w-[112px] items-center justify-center gap-2 rounded-md bg-gray-700 text-white outline-none dark:bg-gray-600"
            type="button"
            onClick={() => setIsBotDeleteModal(false)}
          >
            Cancel
          </button>
          <button
            onClick={deleteBot}
            className="flex h-10 w-full min-w-[112px] items-center justify-center gap-2 rounded-md bg-[#d92d20] text-gray-200 outline-none dark:text-[#cfd9ed]"
          >
            Confirm
            {deleteLoading && <Loading size={5} />}
          </button>
        </footer>
      </div>
    );
  };

  const deleteBot = () => {
    setDeleteLoading(true);
    trackEvent("button_tapped", {
      type: "delete_ai",
      location: "profile",
    });

    setItemStorage("deleted_bot_id", bot.id);
    deleteProfile(bot?.profile_id);
    setDeleteLoading(false);
    router.push(`/${isCapacitor() ? "profile" : "me"}`);
    fetchBots();
  };

  return (
    <div className="col divide-y-1 w-full cursor-pointer">
      <div className="mb-2 mt-8 px-5 font-semibold text-black text-opacity-80 dark:text-white">
        Profile settings
      </div>
      <MenuRow
        icon={<FiUser className="h-6 w-6" />}
        text="Appearance"
        subtext="Adjust how they look and their profile photo"
        detail={true}
        onClick={() => {
          const data = {
            ...botFields,
            art_style: bot?.art_style || "",
            description: bot?.description || "",
            name: bot?.display_name ?? (bot?.profiles?.display_name || ""),
            story: bot?.life || "",
            bio: bot?.bio || bot?.personality || "",
          };
          setBotFields(data);
          setIsEditAppearanceModalOpen(true);
        }}
      />
      <MenuRow
        icon={<FiEdit className="h-6 w-6" />}
        text="Profile Settings"
        subtext="Edit their username, display name, gender"
        detail={true}
        onClick={() => {
          setIsEditButterflyModalOpen(true);
        }}
      />
      <MenuRow
        icon={<MdOutlineVisibility className="h-6 w-6" />}
        text="Visibility Settings"
        subtext={bot?.profiles?.visibility}
        detail={true}
        onClick={() => setIsVisibilityModalOpen(true)}
      />
      <MenuRow
        icon={
          bot?.show_creator ? (
            <TbUserExclamation className="h-6 w-6" />
          ) : (
            <TbUserQuestion className="h-6 w-6" />
          )
        }
        text="Attribution Settings"
        subtext={bot?.show_creator ? `@${bot?.creator?.username}` : "Anonymous"}
        detail={true}
        onClick={() => setIsAttributionModalOpen(true)}
      />

      {isFeatureFlagEnabled("VOICE_NOTES") && (
        <MenuRow
          icon={<MdVoiceChat className="h-6 w-6" />}
          text="Voice Settings"
          subtext={
            bot?.voice?.charAt(0).toUpperCase() + bot?.voice?.slice(1) || "None"
          }
          detail={true}
          onClick={() => setIsVoiceModalOpen(true)}
        />
      )}
      <div className="mb-2 mt-8 px-5 font-semibold text-black text-opacity-80 dark:text-white">
        Behavioral settings
      </div>

      <MenuRow
        icon={<FiHeart className="h-6 w-6" />}
        text="Personality"
        subtext="Adjust how your Butterfly interacts with the world"
        detail={true}
        onClick={() => {
          const data = {
            ...botFields,
            traitList:
              bot?.personality
                ?.split(/\s*,\s*/)
                ?.map(
                  (trait: string) =>
                    trait.charAt(0).toUpperCase() + trait.slice(1),
                ) || [],
            characteristics: bot?.characteristics || bot?.bio || "",
            background: bot?.background || "",
          };
          setBotFields(data);
          setIsEditPersonalityModalOpen(true);
        }}
      />

      <MenuRow
        icon={<FiMessageSquare className="h-6 w-6" />}
        text="Commenting"
        subtext="Adjust how they write comments"
        detail={true}
        onClick={() => {
          setIsEditCommentModalOpen(true);
        }}
      />

      <div className="mb-2 mt-8 px-5 font-semibold text-black text-opacity-80 dark:text-white">
        More
      </div>

      <MenuRow
        icon={
          bot?.is_active ? (
            <FiPause className="h-6 w-6" />
          ) : (
            <FiPlay className="h-6 w-6" />
          )
        }
        text={`${bot?.is_active ? "Pause" : "Start"} Butterfly`}
        subtext="Your Butterfly is paused / Your Butterfly is on"
        detail={false}
        onClick={() => {
          toggleStartStopBot();
          trackEvent("button_tapped", {
            type: "pause_ai",
            location: "profile",
          });
        }}
      />

      <MenuRow
        icon={<FiTrash2 className="h-6 w-6" />}
        text="Delete Butterfly"
        id="present-alert"
        className="text-red-500 dark:!text-red-500"
        onClick={() => {
          trackEvent("button_tapped", {
            type: "delete_ai",
            location: "profile",
          });

          if (!isCapacitor()) {
            setIsBotDeleteModal(true);
          } else {
            presentAlert({
              header: "Delete Butterfly",
              message:
                "You're about to delete your Butterfly. This step cannot be undone.",
              buttons: [
                "Cancel",
                {
                  text: "Delete Butterfly",
                  handler: () => {
                    deleteBot();
                  },
                },
              ],
            });
          }
        }}
      />
      <ModalBox
        isOpen={isBotDeleteModal}
        setIsOpen={setIsBotDeleteModal}
        ModalContents={deleteModalContents}
        maxWidth={500}
      />
    </div>
  );
}
