import { FiD<PERSON>te, FiTrash2 } from "react-icons/fi";
import { updateProfile } from "common/libraries/api";
import { deleteProfile, adminActionLogs } from "common/libraries/adminApi";

const AdminProfileRemoveButton = ({
  profile,
  profiles,
  setProfiles,
}: {
  profile: any;
  profiles: any;
  setProfiles: any;
}) => {
  const onProfileRemoveClick = async (profile: Profile) => {
    const isConfirmed = window.confirm(
      "Are you sure you want to remove this profile?",
    );

    if (!isConfirmed) return;

    try {
      const isHidden = profile?.visibility === "hidden";

      if (!isHidden) {
        const {
          data: { data, error },
        } = await updateProfile({
          updateContents: { visibility: "hidden" },
          profileId: profile?.id,
        });

        if (error) throw error;

        setProfiles((prevProfiles: Profile[]) =>
          prevProfiles.map((item) =>
            item.id === profile?.id ? { ...item, visibility: "hidden" } : item,
          ),
        );
      } else {
        const {
          data: { data, error },
        } = await deleteProfile({
          profileId: profile?.id,
        });

        if (error) throw error;

        setProfiles((prevProfiles: Profile[]) =>
          prevProfiles.filter((item) => item.id !== profile?.id),
        );
      }

      await logAdminAction(profile);
    } catch (error) {
      console.error("Profile operation failed:", error);
      alert(
        `Failed to ${profile?.visibility === "hidden" ? "delete" : "remove"} profile: ${error}`,
      );
    }
  };

  const logAdminAction = async (profile: Profile) => {
    const isHidden = profile?.visibility === "hidden";
    const logData = {
      target: "profiles",
      action: isHidden ? "delete" : "remove",
      info: [
        {
          table: "profiles",
          id: profile?.id,
          ...(isHidden
            ? {}
            : {
                old: { visibility: profile?.visibility },
                new: { visibility: "hidden" },
              }),
        },
      ],
    };

    await adminActionLogs(logData);
  };

  interface Profile {
    id: string;
    visibility: string;
    [key: string]: any;
  }

  return (
    <button
      className={`flex items-center gap-1 rounded bg-red-600 px-2 py-1 text-xs text-white ${
        profile?.visibility == "hidden" && "bg-zinc-800"
      }`}
      onClick={() => onProfileRemoveClick(profile)}
    >
      {profile?.visibility == "hidden" ? (
        <>
          <FiDelete />
          Delete
        </>
      ) : (
        <>
          <FiTrash2 />
          Remove
        </>
      )}
    </button>
  );
};

export default AdminProfileRemoveButton;
