const AdminUserLoadingCards = ({ count }: { count: number }) => {
  return (
    <div className="mt-4 w-full animate-pulse gap-6">
      {Array.from({ length: count }).map((_el, index) => (
        <div
          key={index}
          className="focus:shadow-outline mt-3 flex w-full animate-pulse items-center justify-between overflow-hidden rounded-lg border border-gray-200 bg-white text-left text-sm font-medium text-gray-900 shadow-md hover:shadow-lg focus-visible:ring focus-visible:ring-gray-500/75 dark:border-gray-500 dark:bg-slate-900 dark:text-white"
        >
          <div className="flex w-full items-center p-2">
            <div className="hidden h-24 w-24 flex-col items-center justify-center rounded-t-lg object-cover sm:flex">
              <svg
                className="me-3 h-20 w-20 text-gray-200 dark:text-gray-700"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M10 0a10 10 0 1 0 10 10A10.011 10.011 0 0 0 10 0Zm0 5a3 3 0 1 1 0 6 3 3 0 0 1 0-6Zm0 13a8.949 8.949 0 0 1-4.951-1.488A3.987 3.987 0 0 1 9 13h2a3.987 3.987 0 0 1 3.951 3.512A8.949 8.949 0 0 1 10 18Z" />
              </svg>
            </div>
            <div className="flex flex-grow flex-col gap-6">
              <div className="relative flex w-full justify-start gap-2 sm:justify-between">
                <div className="flex h-24 w-24 flex-col place-items-center items-center justify-center overflow-hidden rounded-lg object-cover sm:hidden">
                  <svg
                    className="me-3 h-20 w-20 text-gray-200 dark:text-gray-700"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M10 0a10 10 0 1 0 10 10A10.011 10.011 0 0 0 10 0Zm0 5a3 3 0 1 1 0 6 3 3 0 0 1 0-6Zm0 13a8.949 8.949 0 0 1-4.951-1.488A3.987 3.987 0 0 1 9 13h2a3.987 3.987 0 0 1 3.951 3.512A8.949 8.949 0 0 1 10 18Z" />
                  </svg>
                </div>
                <div className="flex flex-col gap-1">
                  <div className="h-4 w-56 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                  <div className="h-4 w-56 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                  <div className="h-4 w-56 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                </div>
                <div className="absolute left-0 top-24 flex flex-col items-end sm:right-0 sm:top-0">
                  <div className="flex flex-col justify-end gap-1 text-xs">
                    <div className="flex justify-start gap-1 sm:justify-end">
                      <div className="h-6 w-20 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                      <div className="h-6 w-20 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                    </div>
                    <div className="h-3 w-48 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                    <div className="h-3 w-48 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                  </div>
                </div>
              </div>

              <div className="flex w-full flex-row items-end justify-between gap-3 sm:flex-row">
                <div className="flex gap-3">
                  <div className="h-6 w-24 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                  <div className="h-6 w-24 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                </div>
                <div className="flex flex-col items-end justify-center gap-2 sm:flex-row">
                  <div className="h-6 w-24 rounded-md bg-gray-200 dark:bg-gray-700"></div>
                  <div className="flex gap-1">
                    <div className="h-6 w-20 rounded-md bg-gray-200 dark:bg-gray-700"></div>
                    <div className="h-6 w-20 rounded-md bg-gray-200 dark:bg-gray-700"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
export default AdminUserLoadingCards;
