"use client"; // This is a client component
import { Fragment, Dispatch } from "react";
import { Dialog, Transition } from "@headlessui/react";

import UnauthedLanding from "./UnauthedLanding";

interface DesktopAuthModalBoxProps {
  isOpen: boolean;
  dismiss?: () => void;
}

export default function DesktopAuthModalBox({
  isOpen,
  dismiss,
}: DesktopAuthModalBoxProps) {
  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-10"
        onClose={() => dismiss?.()}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-50" />
        </Transition.Child>

        <div className="fixed inset-0 box-border overflow-y-auto">
          <div className="box-border flex min-h-screen items-center justify-center text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="dark:solid container m-8 mx-4 box-border max-h-screen w-full transform rounded-2xl border-none bg-white text-left align-middle shadow-xl transition-all dark:border dark:border-white-900 dark:bg-black-200 sm:w-2/3 md:w-auto lg:min-w-[350px]">
                <UnauthedLanding parentLocation={"modal"} />
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
