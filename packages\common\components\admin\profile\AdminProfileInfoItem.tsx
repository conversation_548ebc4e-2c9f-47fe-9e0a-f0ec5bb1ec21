"use client";
import React from "react";
import Link from "next/link";

import { isCapacitor } from "common/utils/Helper";
import { AdminGetDescription } from "common/components/admin/post";
import { AdminProfileViewMoreModalButton } from "./buttons";

interface ProfileType {
  id: number;
  username: string;
  displayName: string;
  location: string;
  description: string;
  visibility: string;
  ageType: string | null;
  nsfw: string;
  nsfl: boolean;
  botID: number | null;
}

const AdminProfileInfoItem = ({ profile }: { profile: ProfileType }) => {
  return (
    <>
      {/* Profile Infos */}
      <div className="space-y-1">
        {/* ID & Visit User */}
        <div className="mb-1 flex flex-wrap items-center gap-4">
          {/* ID */}
          <div className="flex gap-2">
            <div>ID:</div>
            <Link
              href="/admin/profiles/[id]"
              as={`/admin/profiles/${profile.id}`}
              className="font-bold underline"
            >
              {profile.id}
            </Link>
          </div>

          <div className="flex overflow-hidden rounded-full">
            {/* Type : Butterfly | Account */}
            <div
              className={`${
                profile.botID
                  ? "border-yellow-400 bg-yellow-100 text-yellow-800 dark:bg-gray-700 dark:text-yellow-400"
                  : "border-cyan-400 bg-cyan-100 text-cyan-800 dark:bg-gray-700 dark:text-cyan-400"
              } rounded-l-full border px-2.5 py-0.5 text-sm font-medium`}
            >
              {profile.botID ? "Butterfly" : "Account"}
            </div>

            <div
              className={`rounded-r-full border px-2.5 py-0.5 text-sm font-medium ${
                profile.visibility === "public"
                  ? "border-green-400 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                  : profile.visibility === "private"
                    ? "border-blue-400 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                    : "border-gray-400 bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
              } `}
            >
              {profile.visibility.charAt(0).toUpperCase() +
                profile.visibility.slice(1)}
            </div>
          </div>

          {profile.ageType && (
            <div
              className={`rounded-full border px-2.5 py-0.5 text-sm font-medium ${
                profile.ageType === "child"
                  ? "border-cyan-400 bg-cyan-100 text-cyan-800"
                  : "border-orange-400 bg-orange-100 text-orange-800"
              }`}
            >
              {profile.ageType === "child" ? "Child" : "Adult"}
            </div>
          )}

          <div className="flex gap-1">
            {profile.nsfw === "nsfw" && (
              <div className="rounded-full border border-red-400 bg-red-100 px-2.5 py-0.5 text-sm font-medium text-red-800 dark:bg-gray-700 dark:text-red-400">
                NSFW
              </div>
            )}

            {profile.nsfl && (
              <div className="rounded-full border border-[#ff4747] bg-[#fcff3a] px-2.5 py-0.5 text-sm font-medium text-[#ff4747] dark:bg-gray-700 dark:text-red-500">
                NSFL
              </div>
            )}
          </div>
        </div>

        {/* Username */}
        <div className="flex gap-2">
          <div>Username:</div>
          <Link
            href={`/users/${profile.username}`}
            className="font-bold underline"
          >
            {profile.username}
          </Link>
        </div>

        {/* Display Name */}
        <div className="flex gap-2">
          <div>Name: </div>
          <p>{profile?.displayName}</p>
        </div>

        {/* Location */}
        {profile.location && (
          <div className="flex gap-2">
            <div>Location:</div>
            <p className="text-ellipsis sm:block">{profile.location}</p>
          </div>
        )}

        {/* Description */}
        {profile.description && (
          <div className="flex flex-col gap-2 md:flex-row">
            <div>Description:</div>
            <p className="col-span-3 flex-grow text-ellipsis">
              {AdminGetDescription(
                profile.description || "",
                isCapacitor() ? 100 : 300,
              )}
            </p>
          </div>
        )}

        {/* View More : Button */}
        {profile.botID && <AdminProfileViewMoreModalButton profile={profile} />}
      </div>
    </>
  );
};

export default AdminProfileInfoItem;
