import Link from "next/link";
import { FiCheckCircle } from "react-icons/fi";

import ResponsiveImage from "common/components/ResponsiveImage";
import AdminProfileInfoItem from "./AdminProfileInfoItem";
import {
  AdminProfileNSFWButton,
  AdminProfileNSFLButton,
  AdminProfileFollowsModalButton,
  AdminBanUserButton,
} from "./buttons";
import { selectedProfileAtom } from "common/state/selectedProfile";
import { useAtomValue } from "jotai";
import { adminActionLogs, adminReivews } from "common/libraries/adminApi";
import { fetchPostUrlsWithProfileId } from "common/libraries/api";
import { wrappedError } from "common/utils/errorUtils";

interface ReviewProfileType {
  id: number;
  avatarURL: string;
  userID: number | null;
  mediaURL: string[];
  gallery: boolean;
  followerCount: number;
  followingCount: number;
  nsfw: string;
  nsfl: boolean;
  visibility: string;
  botID: number | null;
  username: string;
  displayName: string;
  location: string;
  description: string;
}

const AdminReviewProfileItem = ({
  profile,
  profiles,
  setProfiles,
}: {
  profile: ReviewProfileType;
  profiles: any;
  setProfiles: any;
}) => {
  const selectedProfile = useAtomValue(selectedProfileAtom);
  const isAdmin = selectedProfile?.users?.role === "admin";

  const onViewAllClick = async (profileId: number) => {
    try {
      const response = await fetchPostUrlsWithProfileId({
        profile_id: profileId,
      });
      const { data, error } = response.data;

      if (error) {
        throw wrappedError(error, "Error fetching posts url with profile_id");
      }

      if (data && data.length > 0) {
        const _profiles = [...profiles];
        const profile = _profiles.find((profile) => profile.id === profileId);
        if (profile) {
          profile.media_urls = data?.map((item: any) => item.media_url);
        }
        setProfiles(_profiles);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const onDoneClick = async (profile: any) => {
    const isConfirmed = confirm(
      "Could you confirm marking this post as reviewed?",
    );

    if (!isConfirmed) return;

    try {
      const response = await adminReivews("profile_reviews", {
        profile_id: profile?.id,
        reviewer_id: selectedProfile?.id,
      });
      const { data, error } = response.data;

      if (error) throw error;

      setProfiles((prev: any) => prev.filter((p: any) => p.id !== profile.id));
    } catch (error) {
      console.error(error);
    } finally {
      await adminActionLogs({
        target: "profiles",
        target_id: profile.id,
        action: "mark_as_reviewed",
        info: [
          {
            table: "profile_reviews",
            id: profile.id,
          },
        ],
      });
    }
  };

  return (
    <div className="focus:shadow-outline relative mt-3 flex w-full items-center justify-between overflow-hidden rounded-lg rounded-l-full border border-gray-200 bg-white text-left text-sm font-medium text-gray-900 shadow-sm hover:shadow-lg focus-visible:ring focus-visible:ring-gray-500/75 dark:border-gray-500 dark:bg-slate-900 dark:text-white">
      <div className="flex w-full gap-1">
        {/* Avatar of Profile */}
        <div className="hidden h-full w-[240px] shrink-0 items-center overflow-hidden rounded-full object-cover sm:block">
          {profile.avatarURL && (
            <Link href={profile.avatarURL ?? "#"}>
              <ResponsiveImage
                src={profile.avatarURL}
                alt={"Profile Avatar"}
                size={[240, 240]}
              ></ResponsiveImage>
            </Link>
          )}
        </div>

        {/* Posts of Profile */}
        <div className="relative flex flex-col justify-center">
          <div className="absolute top-0 flex w-full flex-row justify-end overflow-x-auto p-1">
            {/* View All : Button */}
            <button
              onClick={(e) => {
                e.currentTarget.style.display = "none";
                onViewAllClick(profile.id);
              }}
              className="font-bold"
            ></button>
          </div>

          {/* Posts */}
          <div className="flex items-center gap-1 overflow-x-auto rounded-lg">
            {profile.mediaURL.map((media_url: string, index: number) => (
              <div
                key={index}
                className="hidden h-[180px] w-[135px] shrink-0 items-center overflow-hidden rounded-lg object-cover sm:block"
              >
                {profile.mediaURL.length > 0 && (
                  <ResponsiveImage
                    src={media_url}
                    alt={"Post 1"}
                    size={[135, 180]}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        {profile.gallery == false && (
          <div className="flex flex-1 flex-col justify-between border-l p-3">
            <AdminProfileInfoItem profile={profile} />

            <div className="flex items-center justify-between gap-2 lg:flex-col 2xl:flex-row">
              {/* Followers & Followings */}
              <AdminProfileFollowsModalButton profile={profile} />

              <div className="flex flex-col items-end justify-end gap-2 lg:flex-row">
                {/* Mark as NSFW */}
                <AdminProfileNSFWButton
                  profile={profile}
                  profiles={profiles}
                  setProfiles={setProfiles}
                />

                {/* Mark as NSFL */}
                <AdminProfileNSFLButton
                  profile={profile}
                  profiles={profiles}
                  setProfiles={setProfiles}
                />

                {/* Ban user */}
                {isAdmin && profile?.userID && (
                  <AdminBanUserButton
                    profile={profile}
                    setProfiles={setProfiles}
                  />
                )}

                {/* Done */}
                <button
                  className={`flex items-center gap-1 rounded bg-lime-600 px-2 py-1 text-xs text-white`}
                  onClick={() => onDoneClick(profile)}
                >
                  <FiCheckCircle />
                  Done
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminReviewProfileItem;
