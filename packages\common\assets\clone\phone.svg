<svg width="316" height="636" viewBox="0 0 316 636" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M311 184C312.869 184 313.804 184 314.5 184.402C314.956 184.665 315.335 185.044 315.598 185.5C316 186.196 316 187.131 316 189L316 250C316 251.869 316 252.804 315.598 253.5C315.335 253.956 314.956 254.335 314.5 254.598C313.804 255 312.869 255 311 255L311 184Z" fill="#161718" style="fill:#161718;fill:color(display-p3 0.0863 0.0902 0.0941);fill-opacity:1;"/>
<path d="M311 184C312.869 184 313.804 184 314.5 184.402C314.956 184.665 315.335 185.044 315.598 185.5C316 186.196 316 187.131 316 189L316 250C316 251.869 316 252.804 315.598 253.5C315.335 253.956 314.956 254.335 314.5 254.598C313.804 255 312.869 255 311 255L311 184Z" fill="url(#paint0_linear_507_1596)" fill-opacity="0.16" style=""/>
<path d="M5 224C3.13079 224 2.19618 224 1.50003 224.402C1.04397 224.665 0.665244 225.044 0.401936 225.5C1.99848e-05 226.196 1.95609e-05 227.131 1.87131e-05 229L2.3857e-06 265C1.53794e-06 266.869 1.11406e-06 267.804 0.401916 268.5C0.665224 268.956 1.04395 269.335 1.50001 269.598C2.19616 270 3.13077 270 4.99998 270L5 224Z" fill="#161718" style="fill:#161718;fill:color(display-p3 0.0863 0.0902 0.0941);fill-opacity:1;"/>
<path d="M5 224C3.13079 224 2.19618 224 1.50003 224.402C1.04397 224.665 0.665244 225.044 0.401936 225.5C1.99848e-05 226.196 1.95609e-05 227.131 1.87131e-05 229L2.3857e-06 265C1.53794e-06 266.869 1.11406e-06 267.804 0.401916 268.5C0.665224 268.956 1.04395 269.335 1.50001 269.598C2.19616 270 3.13077 270 4.99998 270L5 224Z" fill="url(#paint1_linear_507_1596)" fill-opacity="0.16" style=""/>
<path d="M5 168C3.13079 168 2.19618 168 1.50003 168.402C1.04397 168.665 0.665244 169.044 0.401936 169.5C1.99848e-05 170.196 1.95609e-05 171.131 1.87131e-05 173L2.3857e-06 209C1.53794e-06 210.869 1.11406e-06 211.804 0.401916 212.5C0.665224 212.956 1.04395 213.335 1.50001 213.598C2.19616 214 3.13077 214 4.99998 214L5 168Z" fill="#161718" style="fill:#161718;fill:color(display-p3 0.0863 0.0902 0.0941);fill-opacity:1;"/>
<path d="M5 168C3.13079 168 2.19618 168 1.50003 168.402C1.04397 168.665 0.665244 169.044 0.401936 169.5C1.99848e-05 170.196 1.95609e-05 171.131 1.87131e-05 173L2.3857e-06 209C1.53794e-06 210.869 1.11406e-06 211.804 0.401916 212.5C0.665224 212.956 1.04395 213.335 1.50001 213.598C2.19616 214 3.13077 214 4.99998 214L5 168Z" fill="url(#paint2_linear_507_1596)" fill-opacity="0.16" style=""/>
<path d="M5 125C3.13077 125 2.19616 125 1.50001 125.402C1.04395 125.665 0.665234 126.044 0.401928 126.5C6.07809e-06 127.196 5.82376e-06 128.131 5.3151e-06 130L1.50536e-06 144C9.96697e-07 145.869 7.42367e-07 146.804 0.401922 147.5C0.665228 147.956 1.04394 148.335 1.5 148.598C2.19615 149 3.13077 149 4.99999 149L5 125Z" fill="#161718" style="fill:#161718;fill:color(display-p3 0.0863 0.0902 0.0941);fill-opacity:1;"/>
<path d="M5 125C3.13077 125 2.19616 125 1.50001 125.402C1.04395 125.665 0.665234 126.044 0.401928 126.5C6.07809e-06 127.196 5.82376e-06 128.131 5.3151e-06 130L1.50536e-06 144C9.96697e-07 145.869 7.42367e-07 146.804 0.401922 147.5C0.665228 147.956 1.04394 148.335 1.5 148.598C2.19615 149 3.13077 149 4.99999 149L5 125Z" fill="url(#paint3_linear_507_1596)" fill-opacity="0.16" style=""/>
<g filter="url(#filter0_ii_507_1596)">
<rect x="3" width="310" height="635.626" rx="56" fill="#161718" style="fill:#161718;fill:color(display-p3 0.0863 0.0902 0.0941);fill-opacity:1;"/>
<rect x="3" width="310" height="635.626" rx="56" fill="url(#paint4_linear_507_1596)" fill-opacity="0.05" style=""/>
<g filter="url(#filter1_i_507_1596)">
<rect x="17" y="14" width="282" height="607.626" rx="44" fill="url(#paint5_linear_507_1596)" fill-opacity="0.3" style=""/>
<rect x="115.105" y="28" width="85.79" height="31.1964" rx="15.5982" fill="#0F0F11" style="fill:#0F0F11;fill:color(display-p3 0.0592 0.0592 0.0652);fill-opacity:1;"/>
<g opacity="0.4">
<circle cx="185.672" cy="43.5962" r="8.77398" fill="#050312" fill-opacity="0.5" style="fill:#050312;fill:color(display-p3 0.0199 0.0116 0.0705);fill-opacity:0.5;"/>
<g filter="url(#filter2_f_507_1596)">
<g clip-path="url(#clip0_507_1596)">
<ellipse cx="185.667" cy="43.5971" rx="4.87443" ry="4.87443" fill="#221283" fill-opacity="0.2" style="fill:#221283;fill:color(display-p3 0.1320 0.0698 0.5140);fill-opacity:0.2;"/>
<g filter="url(#filter3_f_507_1596)">
<ellipse cx="187" cy="43.6533" rx="3" ry="2" transform="rotate(90 187 43.6533)" fill="url(#paint6_linear_507_1596)" style=""/>
</g>
<g filter="url(#filter4_f_507_1596)">
<ellipse cx="3" cy="2" rx="3" ry="2" transform="matrix(4.37114e-08 1 1 -3.29894e-07 182 40.6533)" fill="url(#paint7_linear_507_1596)" style=""/>
</g>
<g filter="url(#filter5_f_507_1596)">
<ellipse cx="4" cy="2" rx="4" ry="2" transform="matrix(4.37114e-08 1 1 -3.29894e-07 183 39.6533)" fill="#171720" style="fill:#171720;fill:color(display-p3 0.0902 0.0902 0.1255);fill-opacity:1;"/>
</g>
</g>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_ii_507_1596" x="2.99982" y="0" width="310" height="639.626" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_507_1596"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_507_1596" result="effect2_innerShadow_507_1596"/>
</filter>
<filter id="filter1_i_507_1596" x="16.9998" y="14" width="282" height="608.601" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.974886"/>
<feGaussianBlur stdDeviation="3.89954"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_507_1596"/>
</filter>
<filter id="filter2_f_507_1596" x="180.293" y="38.2227" width="10.7488" height="10.7489" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.25" result="effect1_foregroundBlur_507_1596"/>
</filter>
<filter id="filter3_f_507_1596" x="183" y="38.6533" width="8" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_507_1596"/>
</filter>
<filter id="filter4_f_507_1596" x="180" y="38.6533" width="8" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_507_1596"/>
</filter>
<filter id="filter5_f_507_1596" x="181" y="37.6533" width="8" height="12" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_507_1596"/>
</filter>
<linearGradient id="paint0_linear_507_1596" x1="311" y1="219.5" x2="316" y2="219.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0" style="stop-color:none;stop-opacity:0;"/>
<stop offset="1" stop-color="white" style="stop-color:white;stop-opacity:1;"/>
</linearGradient>
<linearGradient id="paint1_linear_507_1596" x1="4.99999" y1="247" x2="1.05494e-05" y2="247" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0" style="stop-color:none;stop-opacity:0;"/>
<stop offset="1" stop-color="white" style="stop-color:white;stop-opacity:1;"/>
</linearGradient>
<linearGradient id="paint2_linear_507_1596" x1="4.99999" y1="191" x2="1.05494e-05" y2="191" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0" style="stop-color:none;stop-opacity:0;"/>
<stop offset="1" stop-color="white" style="stop-color:white;stop-opacity:1;"/>
</linearGradient>
<linearGradient id="paint3_linear_507_1596" x1="5" y1="137" x2="3.41023e-06" y2="137" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0" style="stop-color:none;stop-opacity:0;"/>
<stop offset="1" stop-color="white" style="stop-color:white;stop-opacity:1;"/>
</linearGradient>
<linearGradient id="paint4_linear_507_1596" x1="158" y1="0" x2="158" y2="635.626" gradientUnits="userSpaceOnUse">
<stop stop-color="white" style="stop-color:white;stop-opacity:1;"/>
<stop offset="1" stop-color="white" stop-opacity="0" style="stop-color:none;stop-opacity:0;"/>
</linearGradient>
<linearGradient id="paint5_linear_507_1596" x1="158" y1="14" x2="158" y2="621.626" gradientUnits="userSpaceOnUse">
<stop style="stop-color:black;stop-opacity:1;"/>
<stop offset="1" stop-opacity="0" style="stop-color:none;stop-opacity:0;"/>
</linearGradient>
<linearGradient id="paint6_linear_507_1596" x1="187" y1="41.6533" x2="187" y2="45.6533" gradientUnits="userSpaceOnUse">
<stop stop-color="#5238F3" style="stop-color:#5238F3;stop-color:color(display-p3 0.3210 0.2180 0.9539);stop-opacity:1;"/>
<stop offset="1" stop-color="#67ADFF" style="stop-color:#67ADFF;stop-color:color(display-p3 0.4043 0.6783 1.0000);stop-opacity:1;"/>
</linearGradient>
<linearGradient id="paint7_linear_507_1596" x1="3" y1="0" x2="3" y2="4" gradientUnits="userSpaceOnUse">
<stop stop-color="#B3D6FF" style="stop-color:#B3D6FF;stop-color:color(display-p3 0.7035 0.8399 1.0000);stop-opacity:1;"/>
<stop offset="1" stop-color="#5238F3" style="stop-color:#5238F3;stop-color:color(display-p3 0.3210 0.2180 0.9539);stop-opacity:1;"/>
</linearGradient>
<clipPath id="clip0_507_1596">
<rect x="180.793" y="38.7227" width="9.74886" height="9.74886" rx="4.87443" fill="white" style="fill:white;fill-opacity:1;"/>
</clipPath>
</defs>
</svg>
