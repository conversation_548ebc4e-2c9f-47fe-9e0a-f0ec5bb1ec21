"use client";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useHistory } from "react-router-dom";

import supabase from "common/libraries/supabaseClient";
import Link from "common/utils/Link";
import { isCapacitor } from "common/utils/Helper";
import { useNextRouting } from "common/contexts/NextRoutingContext";
import { useToast } from "common/contexts/ToastContext";
import { SITE_URL, MOBILE_SITE_URL } from "common/config";
import { useContext, useState } from "react";
import { UserContext } from "common/contexts/UserContext";
import { removeItemStorage } from "common/utils/localStorageWrappers";

const validationSchema = Yup.object({
  email: Yup.string().email("Invalid email address").required("Required"),
});

export default function RecoverypwdForm({ setSignin }) {
  const { setRecovery } = useContext(UserContext);
  const { invokeToast } = useToast();

  const [email, setEmail] = useState<string>("");
  const [isVerifyCodeForm, setIsVerifyCodeForm] = useState<boolean>(false);
  const [verifyCode, setVerifyCode] = useState<string>("");
  const [verifyError, setVerifyError] = useState<string>("");

  let router;
  if (isCapacitor()) {
    router = useHistory();
  } else {
    router = useNextRouting();
  }

  const formik = useFormik({
    initialValues: {
      email: "",
    },
    validationSchema,
    onSubmit: async (values, { setSubmitting, setErrors }) => {
      removeItemStorage("returnUrl");
      const { data, error } = await supabase.auth.resetPasswordForEmail(
        values.email,
        {
          redirectTo: isCapacitor() ? MOBILE_SITE_URL : SITE_URL,
        },
      );
      if (error) {
        invokeToast({
          type: "error",
          text: "Something went wrong!",
          position: "top",
        });
        setErrors({ supabase: error.message });
      } else {
        invokeToast({
          type: "info",
          text: "Please confirm your inbox!",
          position: "top",
        });
        setSubmitting(false);

        setEmail(values.email);
        setIsVerifyCodeForm(true);
      }
    },
  });

  const onVerify = async (code: string) => {
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token: `${code}`,
      type: "email",
    });

    if (error) {
      invokeToast({
        type: "error",
        text: "Something went wrong!",
        position: "top",
      });
      setVerifyError(error.message);
    } else {
      setRecovery(true);
      setVerifyError("");
    }
  };

  const handleChangeVerifyCode = (event: any) => {
    const value = event.target.value.replace(/\D/g, ""); // Remove non-digits
    if (value.length <= 6) {
      setVerifyCode(value);
    }

    if (value.length === 6) {
      onVerify(value);
    }
  };

  const handleResend = async () => {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: isCapacitor() ? MOBILE_SITE_URL : SITE_URL,
    });

    if (error) {
      invokeToast({
        type: "error",
        text: "Something went wrong!",
        position: "top",
      });
      setVerifyError(error.message);
    } else {
      invokeToast({
        type: "info",
        text: "Please confirm your inbox!",
        position: "top",
      });
      setVerifyError("");
    }
  };

  return (
    <div className="mx-auto w-full max-w-sm">
      {isVerifyCodeForm ? (
        <div className="mb-4 rounded bg-white px-8 pb-8 pt-6 shadow-md">
          <div className="mb-4 text-center">
            <h2 className="mb-2 text-2xl font-bold">Input verificaton code</h2>
          </div>

          <div className="mb-4">
            <input
              className="mx-4 w-full border-none p-3 text-center text-4xl font-semibold outline-none focus:border-transparent focus:ring-0 dark:bg-black-100 dark:text-secondary"
              id="verifyCode"
              color="primary"
              autoFocus
              placeholder="••••••"
              style={{ letterSpacing: "1.25rem" }}
              value={verifyCode}
              onChange={handleChangeVerifyCode}
            />

            <div className="mt-4 text-center text-sm">
              Verification code sent to <b>{email}</b>
            </div>

            {verifyError && (
              <div className="mt-4 text-center text-sm text-red-500">
                {verifyError}
              </div>
            )}
          </div>

          <div className="flex items-center justify-between">
            <button
              type="submit"
              className="focus:shadow-outline w-full rounded bg-black px-4 py-3 font-semibold text-white hover:bg-blue-700 focus:outline-none"
              onClick={handleResend}
            >
              Resend
            </button>
          </div>
        </div>
      ) : (
        <form
          onSubmit={formik.handleSubmit}
          className="mb-4 rounded bg-white px-8 pb-8 pt-6 shadow-md"
        >
          <div className="mb-4 text-center">
            <h2 className="mb-2 text-2xl font-bold">Forgot Password</h2>
          </div>

          {["email"].map((field, index) => (
            <div className="mb-4" key={index}>
              <input
                {...formik.getFieldProps(field)}
                className="focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"
                id={field}
                type={field === "password" ? "password" : "text"}
                placeholder={
                  field === "fullName"
                    ? "Full Name"
                    : field.charAt(0).toUpperCase() + field.slice(1)
                }
              />
              {formik.touched[field] && formik.errors[field] ? (
                <div className="text-sm text-red-500">
                  {formik.errors[field]}
                </div>
              ) : null}
            </div>
          ))}
          <div className="flex items-center justify-between">
            <button
              type="submit"
              className="focus:shadow-outline w-full rounded bg-black px-4 py-3 font-semibold text-white hover:bg-blue-700 focus:outline-none"
              disabled={formik.isSubmitting}
            >
              Send a verification code
            </button>
          </div>
          {formik.errors.supabase && (
            <div className="mt-4 text-sm text-red-500">
              {formik.errors.supabase}
            </div>
          )}
        </form>
      )}
      <p className="text-center text-sm">
        Have an account?
        <Link
          href="/users/signin/email"
          className="ml-1 text-blue-500 hover:text-blue-800"
          onClick={() => {
            if (setSignin) {
              setSignin(true);
            }
          }}
        >
          Log in
        </Link>
      </p>
    </div>
  );
}
