"use client";
import { useContext } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useHistory } from "react-router-dom";

import supabase from "common/libraries/supabaseClient";
import { UserContext } from "common/contexts/UserContext";
import Link from "common/utils/Link";
import { isCapacitor } from "common/utils/Helper";
import { useNextRouting } from "common/contexts/NextRoutingContext";
import { getItemStorage } from "common/utils/localStorageWrappers";

const validationSchema = Yup.object({
  email: Yup.string().email("Invalid email address").required("Required"),
  password: Yup.string()
    .min(8, "Must be at least 8 characters")
    .required("Required"),
});

const formFields = ["email", "password"] as const;

const formFieldTypes = {
  email: "email",
  password: "password",
};

const formFieldPlaceholders = {
  email: "Email",
  password: "Password",
};

const formFieldAutocomplete = {
  email: "email",
  password: "current-password",
};

export default function SigninForm() {
  const { loadUser, loadUserData, setRecovery } = useContext(UserContext);

  let router: any;

  if (isCapacitor()) {
    router = useHistory();
  } else {
    router = useNextRouting();
  }

  const formik = useFormik({
    initialValues: {
      email: "",
      password: "",
    },
    validationSchema,
    onSubmit: async (values, { setSubmitting, setErrors }) => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: values.email,
        password: values.password,
      });
      if (error) {
        console.log("ERRORS", error);
        setErrors({ supabase: error.message });
      } else {
        const returnUrl = getItemStorage("returnUrl");
        // Redirect or handle successful signin
        await loadUser();
        await loadUserData(data?.user);

        if (
          isCapacitor() ||
          !returnUrl ||
          returnUrl == "undefined" ||
          returnUrl == "null"
        )
          router.replace("/");
      }
      setSubmitting(false);
    },
  });

  return (
    <div className="mx-auto mt-20 w-full max-w-sm">
      <form
        onSubmit={formik.handleSubmit}
        className="mb-4 rounded bg-white px-8 pb-8 pt-6 text-black shadow-md dark:bg-black-100 dark:text-white"
      >
        <div className="mb-4 text-center">
          <h2 className="mb-2 text-2xl font-bold">Log in</h2>
        </div>

        {formFields.map((field, index) => (
          <div className="mb-4" key={index}>
            <input
              {...formik.getFieldProps(field)}
              className="focus:shadow-outline w-full appearance-none rounded border bg-white px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none dark:bg-black-100 dark:text-white"
              id={field}
              type={formFieldTypes[field]}
              placeholder={formFieldPlaceholders[field]}
              autoComplete={formFieldAutocomplete[field]}
            />
            {formik.touched[field] && formik.errors[field] ? (
              <div className="text-sm text-red-500">{formik.errors[field]}</div>
            ) : null}
          </div>
        ))}
        <div className="mb-4 flex items-center justify-between">
          <button
            type="submit"
            className="focus:shadow-outline w-full rounded-lg bg-black px-4 py-3 font-bold text-white hover:bg-blue-700 focus:outline-none dark:bg-[#4285F4]"
            disabled={formik.isSubmitting}
          >
            Log in
          </button>
        </div>

        <div className="text-center" onClick={() => setRecovery(false)}>
          <Link
            href="/users/resetpassword"
            className="ml-1 text-blue-500 hover:text-blue-800"
          >
            Forgot password?
          </Link>
        </div>

        {formik.errors.supabase && (
          <div className="mt-4 text-sm text-red-500">
            {formik.errors.supabase}
          </div>
        )}
      </form>
      <p className="text-center text-sm text-black dark:text-white">
        Don't have an account?
        <Link
          href="/users/signup/email"
          className="ml-1 text-blue-500 hover:text-blue-800"
        >
          Sign up
        </Link>
      </p>
    </div>
  );
}
