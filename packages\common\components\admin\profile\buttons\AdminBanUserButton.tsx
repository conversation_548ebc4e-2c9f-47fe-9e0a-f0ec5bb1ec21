import { FiDelete } from "react-icons/fi";

import { adminActionLogs, banAccount } from "common/libraries/adminApi";

const AdminBanUserButton = ({
  profile,
  setProfiles,
}: {
  profile: any;
  setProfiles: any;
}) => {
  const onBanUserClick = async (profileId: number, userId: string | null) => {
    if (!userId) return;

    const isConfirmed = window.confirm(
      "Are you sure you want to ban this user?",
    );
    if (!isConfirmed) return;

    try {
      const { data } = await banAccount({
        is_banned: true,
        bannedId: userId,
      });

      if (data.error) {
        throw new Error(data.error);
      }

      setProfiles((prevProfiles: any[]) =>
        prevProfiles.filter((profile) => profile.id !== profileId),
      );

      await adminActionLogs({
        target: "users",
        action: "ban_user",
        info: [
          {
            table: "users",
            id: userId,
            old: { is_banned: false },
            new: { is_banned: true },
          },
        ],
      });
    } catch (error) {
      window.alert(
        `Failed to ban user: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  };

  return (
    <button
      className={`flex items-center gap-1 rounded border bg-gray-900 px-2 py-1 text-xs text-gray-100`}
      onClick={() => {
        onBanUserClick(profile?.id, profile?.userID);
      }}
    >
      {<FiDelete />}
      {"Ban User"}
    </button>
  );
};

export default AdminBanUserButton;
