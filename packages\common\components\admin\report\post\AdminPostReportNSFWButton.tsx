import { Dispatch } from "react";
import { FiFlag } from "react-icons/fi";

import { AdminPostReportType } from "common/components/admin/report/post/AdminPostReportItem";
import { adminActionLogs, updatePostAdmin } from "common/libraries/adminApi";
import { wrappedError } from "common/utils/errorUtils";

const AdminPostReportNSFWButton = ({
  postID,
  nsfw,
  setReports,
}: {
  postID: number;
  nsfw: string;
  setReports: Dispatch<React.SetStateAction<AdminPostReportType[]>>;
}) => {
  /// handle "Mark as NSFW" button click
  const handleMarkAsNSFWClick = async () => {
    try {
      const response = await updatePostAdmin({
        post_id: postID,
        contents: nsfw == "nsfw" ? { nsfw: "normal" } : { nsfw: "nsfw" },
      });
      const { data, error } = response.data;

      if (error) {
        throw wrappedError(error, "Error in handleMarkasNSFW");
      }

      setReports((reports) =>
        reports.map((report) => {
          if (report.post.id == postID) {
            report.post.nsfw = nsfw == "nsfw" ? "normal" : "nsfw";
          }

          return report;
        }),
      );
    } catch (error) {
      console.error(error);
    } finally {
      await adminActionLogs({
        target: "post_reports",
        action: nsfw === "normal" ? "mark_as_nsfw" : "unmark_as_nsfw",
        info: [
          {
            table: "posts",
            id: postID,
            old: {
              nsfw: nsfw == "nsfw" ? "nsfw" : "normal",
            },
            new: {
              nsfw: nsfw == "nsfw" ? "normal" : "nsfw",
            },
          },
        ],
      });
    }
  };

  return (
    <button
      onClick={handleMarkAsNSFWClick}
      className={`${
        nsfw == "nsfw" ? "bg-green-600" : "bg-pink-600"
      } flex w-fit items-center gap-1 rounded px-2 py-1 text-xs text-white`}
    >
      <FiFlag />
      {nsfw == "nsfw" ? "Unmark as NSFW" : "Mark as NSFW"}
    </button>
  );
};

export default AdminPostReportNSFWButton;
