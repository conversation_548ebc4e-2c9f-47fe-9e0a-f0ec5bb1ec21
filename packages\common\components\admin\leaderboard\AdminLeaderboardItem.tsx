"use client";

import { LeaderboardSubmission } from "../../../types/leaderboard";
import dayjs from "dayjs";
import Link from "next/link";
import ResponsiveImage from "common/components/ResponsiveImage";
import { FiExternalLink } from "react-icons/fi";
import DropdownListbox from "common/components/admin/DropdownListbox";
import supabase from "common/libraries/supabaseClient";
import { useState } from "react";
import { apiClient } from "common/libraries/apiClient";

interface AdminLeaderboardItemProps {
  submission: LeaderboardSubmission;
}

export default function AdminLeaderboardItem({
  submission,
}: AdminLeaderboardItemProps) {
  const [status, setStatus] = useState(submission.status);
  const [isGeneratingVideo, setIsGeneratingVideo] = useState(false);

  console.log("submission", submission);

  const generateVideo = async () => {
    try {
      setIsGeneratingVideo(true);
      await apiClient.post(
        "https://cocoon.butterflies.ai/v1/videos/generateVideoForPost",
        {
          postId: submission.post?.id.toString(),
        },
      );
      console.log("Video generation started for post:", submission.post?.id);
    } catch (error) {
      console.error("Error generating video:", error);
    } finally {
      setIsGeneratingVideo(false);
    }
  };

  return (
    <div className="relative mb-4 overflow-hidden rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
      <div className="flex">
        {/* Left side - Image */}
        <Link
          href={`/admin/posts/${submission.post?.id}`}
          className="relative h-[200px] w-[200px] flex-shrink-0 overflow-hidden"
        >
          {submission.post?.media_url && (
            <ResponsiveImage
              src={submission.post.media_url}
              alt="Post media"
              size={[200, 200]}
              autoHeight
              cover
            />
          )}
        </Link>

        {/* Right side - Content */}
        <div className="flex flex-1 flex-col p-4">
          <div className="mb-4 flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link
                href={`/admin/posts/${submission.post?.id}`}
                className="flex items-center gap-2 text-sm font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Post #{submission.post?.id}
                <FiExternalLink className="h-4 w-4" />
              </Link>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {dayjs(submission.created_at).format("MMM D, YYYY h:mm A")}
              </span>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  Status:
                </span>
                <DropdownListbox
                  selectedValue={{
                    value: status,
                    label: status,
                  }}
                  setSelectedValue={async (newStatus: {
                    value: string;
                    label: string;
                  }) => {
                    try {
                      const { error } = await supabase
                        .from("leaderboard_submissions")
                        .update({ status: newStatus.value })
                        .eq("id", submission.id);

                      if (error) throw error;
                      setStatus(newStatus.value);
                      console.log("Status updated to:", newStatus.value);
                    } catch (error) {
                      console.error("Error updating status:", error);
                    }
                  }}
                  valueList={[
                    { value: "pending", label: "Pending" },
                    { value: "archived", label: "Archived" },
                    { value: "deleted", label: "Deleted" },
                    { value: "public", label: "Public" },
                  ]}
                  placeholder="Select Status"
                />
              </div>
            </div>
            <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
              XP: {submission.total_xp}
            </div>
            {submission.post?.video_url ? (
              <Link
                href={submission.post.video_url}
                target="_blank"
                className="ml-4 rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
              >
                Open Video
              </Link>
            ) : (
              <button
                onClick={generateVideo}
                disabled={isGeneratingVideo}
                className="ml-4 rounded-md bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 disabled:bg-blue-300"
              >
                {isGeneratingVideo ? "Generating..." : "Generate Video"}
              </button>
            )}
          </div>

          {/* Description */}
          {submission.post?.description && (
            <div className="mb-4">
              <div className="text-sm text-gray-900 dark:text-gray-100">
                {submission.post.description}
              </div>
            </div>
          )}

          {/* Footer Info */}
          <div className="mt-auto flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Submission #{submission.id}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                User #{submission.user_id}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
