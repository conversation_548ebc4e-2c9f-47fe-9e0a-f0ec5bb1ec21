"use client";
import { Formik, Form, Field, ErrorMessage } from "formik";
import { useContext, useEffect, useMemo, useState } from "react";
import * as Yup from "yup";
import { useAtomValue } from "jotai";

import SafeSessionStorage from "common/utils/SafeSessionStorage";
import { ChatContext } from "common/contexts/ChatContext";
import {
  DEFAULT_LLM_MODEL_LIST,
  OptionType,
} from "common/components/admin/AdminConstants";
import { selectedProfileAtom } from "common/state/selectedProfile";
import { deleteConversations } from "common/libraries/api";
import {
  getModelsLLM,
  updateLLMAndChatmodeAdmin,
} from "common/libraries/adminApi";

const ConversationForm = ({
  conversation,
  promptId,
}: {
  conversation: any;
  promptId: number;
}) => {
  const selectedProfile = useAtomValue(selectedProfileAtom);
  const { conversations, createConversation } = useContext(ChatContext);
  const [llmList, setLlmList] = useState<OptionType[]>(DEFAULT_LLM_MODEL_LIST);

  useEffect(() => {
    fetchLlmList();
  }, []);

  const sendeeProfile = useMemo(
    () =>
      conversation?.participants?.find((e: { profile_id: any }) => {
        return e?.profile_id !== selectedProfile?.id;
      }),
    [conversation, selectedProfile],
  );

  const fetchLlmList = async () => {
    try {
      const { data, error } = (await getModelsLLM()).data;

      if (error) {
        throw error;
      }

      setLlmList(data);
    } catch (error) {
      console.error("Error occurred while fetching llm models", error);
    }
  };

  return (
    <Formik
      enableReinitialize={true}
      initialValues={{
        llm: conversation?.llm || "",
        chat_mode: conversation?.chat_mode || null,
        profile_id: sendeeProfile?.profile_id || null,
        action: "",
      }}
      validationSchema={Yup.object({
        profile_id: Yup.number().min(1).required("Required"),
      })}
      onSubmit={async (values, { setSubmitting }) => {
        // return;
        let conversationId: number;
        let existedConversation = conversations.find((conversation) =>
          conversation.participants.some(
            (participant: any) =>
              participant.profile_id === values.profile_id &&
              values.profile_id !== selectedProfile?.id,
          ),
        );

        if (values.action === "resetConvo") {
          // Handle conversation reset

          if (conversation) {
            // // Delete old conversation
            const { error: conversationDeletionError } = (
              await deleteConversations([conversation?.conversation_id])
            ).data;
            if (conversationDeletionError) throw conversationDeletionError;
          }

          existedConversation = null;
        }

        if (existedConversation) {
          conversationId = existedConversation?.conversation_id;

          try {
            const { error } = (
              await updateLLMAndChatmodeAdmin({
                id: conversationId,
                llm: values.llm,
                chat_mode: values.chat_mode,
              })
            ).data;

            if (error) throw error;
          } catch (error) {
            console.error(error);
          }
        } else {
          const { data, error } = await createConversation(
            "single",
            selectedProfile.id,
            [values.profile_id],
            {
              chat_mode: values.chat_mode ?? "roleplay",
              llm: values.llm ?? "llm",
              chat_length: "balanced",
              conversation_prompt_id: promptId,
            },
            null,
          );
          if (error) {
            console.error("Create Conversation Error: ", error);
            return;
          }

          const { conversation_id } = data[0] ?? {};
          conversationId = conversation_id;
        }

        await SafeSessionStorage.setItem("debugConvo", conversationId);
        window.location.reload();
      }}
    >
      {({ values, setFieldValue }) => (
        <Form className="space-y-4 rounded border bg-white p-4">
          <div className="flex flex-col space-y-2">
            <label htmlFor="profile_id" className="text-lg font-medium">
              Bot Profile ID
            </label>
            <Field
              name="profile_id"
              type="number"
              className="rounded border p-2"
            />
            <ErrorMessage
              name="profile_id"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label htmlFor="llm" className="text-lg font-medium">
              LLM (Changes the underlying model)
            </label>
            <Field
              name="llm"
              type="number"
              className="rounded border p-2"
              as="select"
            >
              {llmList?.map((llm, index) => {
                return (
                  <option key={index} value={llm.value}>
                    {llm.label}
                  </option>
                );
              })}
            </Field>
            <ErrorMessage
              name="llm"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-col space-y-2">
            <label htmlFor="chat_mode" className="text-lg font-medium">
              {`Chat mode (Doesn't do anything)`}
            </label>
            <Field
              name="chat_mode"
              type="number"
              className="rounded border p-2"
              as="select"
            >
              <option value="roleplay">roleplay</option>
              <option value="realism">realism</option>
            </Field>
            <ErrorMessage
              name="chat_mode"
              component="div"
              className="text-sm text-red-500"
            />
          </div>

          <div className="flex flex-wrap justify-center">
            <div className="mr-4 mt-4 flex justify-center gap-3">
              <button
                type="submit"
                onClick={() => setFieldValue("action", "newConvo")}
                className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
              >
                Start new conversation
              </button>
              <button
                type="submit"
                onClick={() => setFieldValue("action", "resetConvo")}
                className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
              >
                Reset convo
              </button>
              {/* Submit button */}
              <button type="submit" style={{ display: "none" }}></button>
            </div>

            <div className="mt-4 flex justify-center"></div>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default ConversationForm;
