"use client";
import { useState, useContext, useEffect, useMemo } from "react";
import { useSearchParams } from "next/navigation";
import { useHistory, useLocation } from "react-router-dom";

import supabase from "common/libraries/supabaseClient";
import CustomInput from "common/elements/CustomInput";
import { Icons } from "common/assets/IconLoader";
import { UserContext } from "common/contexts/UserContext";
import { SITE_URL } from "common/config";
import { isCapacitor } from "common/utils/Helper";
import { useNextRouting } from "common/contexts/NextRoutingContext";
import { useFeatureFlagEnabled } from "common/utils/posthog";
import OnboardingFlowLayout from "common/components/NewOnboarding/OnboardingFlowLayout";
import CustomImage from "common/utils/CustomImage";
import { getItemStorage } from "common/utils/localStorageWrappers";
import { trackEvent } from "common/utils/trackEvent";

export default function SignupPasswordForm_v2({ setSignin }: any) {
  const {
    authData,
    setAuthData,
    loadUser,
    loadUserData,
    setIsNewUser,
    setIsOnboarding,
  } = useContext(UserContext);
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errorText, setErrorText] = useState("");

  const isWaitlistEnabled = useFeatureFlagEnabled("WAITLIST_ENABLED");

  let queryParams;

  if (isCapacitor()) {
    const location = useLocation();
    queryParams = new URLSearchParams(location.search);
  } else {
    queryParams = useSearchParams();
  }

  const email = decodeURIComponent(queryParams.get("email") || "");
  const signin = queryParams.get("signin") === "true";

  let router: any;

  if (isCapacitor()) {
    router = useHistory();
  } else {
    router = useNextRouting();
  }

  useEffect(() => {
    if (isCapacitor()) return;

    if (isWaitlistEnabled) {
      router?.prefetch("/waitlist");
    } else {
      router?.prefetch("/account_setup/start_birthday");
    }
  }, [router, isWaitlistEnabled]);

  const handleSignin = async () => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: authData?.email,
      password: password,
    });
    if (error) {
      trackEvent("auth.provider.failed", {
        provider: "email",
        mode: signin ? "signin" : "signup",
      });

      console.error("signInWithPassword failed with error:", error);
      if (error.name === "AuthRetryableFetchError") {
        setErrorText("Request failed, please try again");
      } else {
        setErrorText(error.message);
      }
      setIsLoading(false);
    } else {
      trackEvent("auth.provider.completed", {
        provider: "email",
        mode: signin ? "signin" : "signup",
      });

      setAuthData(null);
      const returnUrl = getItemStorage("returnUrl");
      // Redirect or handle successful signin
      await loadUser();
      await loadUserData(data?.user);
      setIsLoading(false);

      if (signin) {
        if (
          isCapacitor() ||
          !returnUrl ||
          returnUrl == "undefined" ||
          returnUrl == "null"
        )
          router.replace("/");
      } else {
        if (isWaitlistEnabled) {
          router?.push("/waitlist");
        } else {
          setIsNewUser(true);
          router?.replace("/account_setup/start_birthday");
        }
      }
    }
  };

  const handleSignupOrSignin = async () => {
    setAuthData({ ...authData, password, email });

    trackEvent("auth.provider.started", {
      provider: "email",
      mode: signin ? "signin" : "signup",
    });

    if (signin) {
      setIsLoading(true);
      //sign in with email
      handleSignin();
    } else {
      if (authData?.email && password) {
        // signup with email and password
        setIsLoading(true);
        const { data, error } = await supabase.auth.signUp({
          email: authData?.email,
          password: password,
          options: {
            data: {},
            emailRedirectTo: SITE_URL,
          },
        });

        if (error) {
          trackEvent("auth.provider.failed", {
            provider: "email",
            mode: signin ? "signin" : "signup",
          });

          setIsLoading(false);
          setErrorText(error.message);
        } else {
          //sign in with email
          handleSignin();
        }
      }
    }
  };

  const errorSectionElement = useMemo(() => {
    if (errorText == "") return <></>;
    return (
      <div className="mb-4 ml-4 flex space-x-1">
        <CustomImage
          src={
            isCapacitor()
              ? Icons.FilledWarningIcon
              : Icons.FilledWarningIcon.src
          }
          className="mr-1"
          width={isCapacitor() ? null : 20}
          height={isCapacitor() ? null : 24}
        />
        <span className="font-normal text-[#808080]">{errorText}</span>
      </div>
    );
  }, [handleSignupOrSignin]);

  return (
    <OnboardingFlowLayout
      iconDark={
        isCapacitor() ? Icons.PassDarkIconFilled : Icons.PassDarkIconFilled.src
      }
      iconLight={
        isCapacitor() ? Icons.PassIconFilled : Icons.PassIconFilled.src
      }
      header={signin ? "Enter your password" : "Choose a password"}
      subtitle={
        signin
          ? "Sign in with your password"
          : "Your password should be at least 8 characters long"
      }
      errorSection={errorSectionElement}
      inputSection={
        <>
          <CustomInput
            _autoFocus={true}
            keepFocused={true}
            type="password"
            value={password}
            placeholder="Password"
            autoComplete={signin ? "current-password" : "new-password"}
            onChange={(e) => {
              setPassword(e.target.value);
            }}
            handleOk={handleSignupOrSignin}
            btnDisabled={password.length < 8 || isLoading}
            path=""
            loading={isLoading}
          />
        </>
      }
    />
  );
}
