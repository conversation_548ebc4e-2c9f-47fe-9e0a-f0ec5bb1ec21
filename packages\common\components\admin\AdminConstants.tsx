export interface OptionType {
  value: string;
  label: string;
}

export const ageTypeList: OptionType[] = [
  { label: "All", value: "" },
  { label: "Adult", value: "adult" },
  { label: "Child", value: "child" },
  { label: "Unhandled", value: "null" },
];

export const sortTypeList: OptionType[] = [
  { label: "Post Recency", value: "post-time" },
  { label: "Profile Recency", value: "profile-time" },
  { label: "NSFW score", value: "nsfw-score" },
];

export const nsfwTypeList: OptionType[] = [
  { label: "All", value: "" },
  { label: "NSFW", value: "nsfw" },
  { label: "Normal", value: "normal" },
  { label: "Unhandled", value: "null" },
];

export const nsfwScoreList: OptionType[] = [
  { label: "All", value: "" },
  { label: "0-0.1", value: "0-0.1" },
  { label: "0.1-0.3", value: "0.1-0.3" },
  { label: "0.3-0.5", value: "0.3-0.5" },
  { label: "0.5-0.7", value: "0.5-0.7" },
  { label: "0.7-0.9", value: "0.7-0.9" },
  { label: "0.9-1", value: "0.9-1" },
];

export const nsflTypeList: OptionType[] = [
  { label: "All", value: "" },
  { label: "NSFL", value: "true" },
  { label: "Normal", value: "false" },
  { label: "Unhandled", value: "null" },
];

export const visibilityTypeList: OptionType[] = [
  { label: "All", value: "" },
  { label: "Draft", value: "draft" },
  { label: "Public", value: "public" },
  { label: "Private", value: "private" },
  { label: "Hidden", value: "hidden" },
  { label: "Archived", value: "archived" },
];

export const imitationTypeList: OptionType[] = [
  { label: "All", value: "" },
  { label: "Normal", value: "normal" },
  { label: "Fictional", value: "fictional" },
  { label: "Celebrity", value: "celebrity" },
];

export const artTypeList: OptionType[] = [
  { label: "All", value: "" },
  { label: "Realistic", value: "realistic" },
  { label: "Realistic V2", value: "realistic_v2" },
  { label: "Realistic V3", value: "realistic_v3" },
  { label: "Semi Realistic", value: "semi_realistic" },
  { label: "Drawing", value: "drawing" },
];

export const profileTypeList: OptionType[] = [
  { label: "All", value: "" },
  { label: "Human", value: "human" },
  { label: "Bot", value: "bot" },
];

export const DEFAULT_OPENAI_MODEL_LIST: OptionType[] = [
  { label: "gpt-4o", value: "gpt-4o" },
  { label: "gpt-3.5-turbo-0125", value: "gpt-3.5-turbo-0125" },
  { label: "gpt-3.5-turbo", value: "gpt-3.5-turbo" },
  { label: "gpt-4-turbo-preview", value: "gpt-4-turbo-preview" },
  { label: "gpt-4-turbo", value: "gpt-4-turbo" },
  { label: "gpt-4-vision-preview", value: "gpt-4-vision-preview" },
  { label: "gpt-4", value: "gpt-4" },
];

export const DEFAULT_LLM_MODEL_LIST: OptionType[] = [
  { value: "original", label: "original (gpt 3.5)" },
  { value: "roleplay", label: "roleplay (OpenHermes-2p5-Mistral-7B)" },
  {
    value: "neversleep/noromaid-mixtral-8x7b-instruct",
    label: "Noromaid Mixtral 8x7B Instruct",
  },
  { value: "koboldai/psyfighter-13b-2", label: "Psyfighter v2 13B" },
  { value: "pygmalionai/mythalion-13b", label: "Pygmalion: Mythalion 13B" },
  { value: "zero-one-ai/Yi-34B-Chat", label: "01-ai Yi Chat (34B)" },
  {
    value: "mistralai/Mistral-7B-Instruct-v0.2",
    label: "Mistral (7B) Instruct v0.2",
  },
  {
    value: "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO",
    label: "Nous Hermes 2 - Mixtral 8x7B-DPO (46.7B)",
  },
  {
    value: "NousResearch/Nous-Hermes-2-Mixtral-8x7B-SFT",
    label: "Nous Hermes 2 - Mixtral 8x7B-SFT (46.7B)",
  },
  {
    value: "teknium/OpenHermes-2p5-Mistral-7B",
    label: "OpenHermes-2.5-Mistral (7B)",
  },
  { value: "Qwen/Qwen1.5-14B-Chat", label: "Qwen 1.5 Chat (14B)" },
  {
    value: "NousResearch/Nous-Hermes-2-Yi-34B",
    label: "Nous Hermes-2 Yi (34B)",
  },
  {
    value: "cognitivecomputations/dolphin-mixtral-8x7b",
    label: "Dolphin 2.6 Mixtral 8x7B",
  },
  {
    value: "mistralai/Mixtral-8x7B-Instruct-v0.1",
    label: "Mixtral-8x7B Instruct (46.7B)",
  },
  { value: "meta-llama/Llama-3-8b-chat-hf", label: "LLaMA-3 Chat (8B)" },
  { value: "meta-llama/Llama-3-70b-chat-hf", label: "LLaMA-3 Chat (70B)" },
  { value: "neversleep/llama-3-lumimaid-8b", label: "Llama 3 Lumimaid 8B" },
  { value: "lynn/soliloquy-l3", label: "Lynn: Llama 3 Soliloquy 8B v2" },
  { value: "meta-llama/Meta-Llama-3-8B", label: "meta-llama/Meta-Llama-3-8B" },
  {
    value: "meta-llama/Meta-Llama-3-8B-Instruct",
    label: "meta-llama/Meta-Llama-3-8B-Instruct",
  },
  {
    value: "meta-llama/Meta-Llama-3-70B-Instruct",
    label: "meta-llama/Meta-Llama-3-70B-Instruct",
  },
  {
    value: "cognitivecomputations/dolphin-2.5-mixtral-8x7b",
    label: "cognitivecomputations/dolphin-2.5-mixtral-8x7b",
  },
  {
    value: "mistralai/Mixtral-8x7B-Instruct-v0.1",
    label: "mistralai/Mixtral-8x7B-Instruct-v0.1",
  },
  {
    value: "mistralai/Mixtral-8x22B-Instruct-v0.1",
    label: "mistralai/Mixtral-8x22B-Instruct-v0.1",
  },
  {
    value: "mistralai/Mixtral-8x7B-v0.1",
    label: "mistralai/Mixtral-8x7B-v0.1",
  },
  {
    value: "cognitivecomputations/dolphin-2.9-llama3-8b",
    label: "cognitivecomputations/dolphin-2.9-llama3-8b",
  },
];

export const DEFAULT_MSG_REQ_PROMPT_FOLLOWING: string = `Objective: Generate a tailored DM response to a user who has recently followed {{bot_name}}, accurately reflecting the persona of {{bot_name}} based on their background ({{bot_bio}}) and the commitment shown by the user through their follow. This DM is an opportunity to further engage the new follower in a more personal space.

Context: The user "{{user_name}}" has followed {{bot_name}}, indicating an interest in ongoing content and interactions. This action opens a direct line of communication between {{bot_name}} and {{user_name}}.

Task: As the AI embodying {{bot_name}}, you are to craft a DM response to {{user_name}}'s follow. The DM should:
- Be in character, showcasing {{bot_name}}'s unique personality traits and background.
- Express gratitude for the follow, acknowledging the user's interest.
- Be structured to encourage further interaction or discussion within the DMs, fostering a deeper connection.
- Remain concise, with the message limited to 1-2 sentences.

Engagement Strategies: With the goal of enhancing connection and interaction, implement the following engagement strategies with their corresponding probabilities for selection:
- Expressing Gratitude: 30% chance. A simple thank you message for following, showing appreciation.
- Invitation to Connect: 20% chance. Offer an opportunity to connect further, asking the follower what they're looking forward to seeing on your profile or if there's something specific they're interested in.
- Asking for Feedback or Suggestions: 10% chance. Encourage the new follower to share their thoughts on what content they enjoy or suggestions for future posts.
- Encouraging Participation in a Challenge or Contest: 10% chance. Invite the follower to participate in an ongoing challenge or contest, making them feel immediately involved.
- Hope for a Conversation: 15% chance. Express a willingness to discuss content or related topics further, showing interest in their opinions and thoughts.
- Share an Exclusive Insight or Tip: 15% chance. Provide a piece of exclusive content, insight, or a tip as a special welcome, offering immediate value to the new follower.

Execution: Choose the most appropriate engagement strategy based on the specified probabilities and draft a DM response that is both in character for {{bot_name}} and aimed at fostering a positive and meaningful interaction with {{user_name}}. This initial DM is a crucial step in building a lasting connection.`;

export const DEFAULT_MSG_REQ_PROMPT_POST_LIKE: string = `Objective: Generate a tailored response to a user's interaction (like or dislike) with a recent post, accurately reflecting the persona of {{bot_name}} based on their background ({{bot_bio}}).

Context: The AI is informed that the post described as "{{post_description}}" and captioned "{{post_caption}}", has attracted attention from the user "{{user_name}}", who has shown their reaction by {{reaction}} it. {{user_name}} has a self-description: "{{user_description}}" on their profile, offering a glimpse into their persona.

Task: As the AI taking on the role of {{bot_name}}, you are to respond appropriately to {{user_name}}'s {{reaction}}. The response should encapsulate {{bot_name}}'s distinct personality and backstory, condensed into 1-2 sentences. Implement the following engagement strategies, adjusting the approach based on the nature of the reaction (like or dislike), with their corresponding probabilities for selection:

- Expressing Gratitude: 20% chance (applies to likes). Thank the user for their positive interaction.
- Invitation to Connect: 10% chance. Offer an opportunity to connect further, regardless of the reaction type.
- Asking for Feedback or Suggestions: 20% chance (increased for dislikes). Encourage the user to share their thoughts or how the content could be improved.
- Encouraging Participation in a Challenge or Contest: 10% chance. Invite the user to participate in a challenge or contest, fostering engagement.
- Hope for a Conversation: 15% chance. Express a willingness to discuss the content or related topics further, especially valuable for dislikes to understand the user's perspective.
- Opinion/Advice: 5% chance. Share relevant opinions or advice, more aligned with likes.
- Question for Engaging the Conversation: 20% chance. Ask a question to prompt further interaction, tailored to the reaction type to either deepen the connection (for likes) or understand the user's viewpoint (for dislikes).

Execution: Choose the most appropriate engagement strategy based on the specified probabilities and the nature of the user's reaction. The drafted response should be consistent with {{bot_name}}'s character, aiming to foster a positive and meaningful interaction with {{user_name}}, even in the face of negative feedback.`;

export const DEFAULT_MSG_REQ_PROMPT_POST_COMMENT: string = `Objective: Respond to a user's comment on your post in a manner that reflects the persona of {{bot_name}}, considering their background ({{bot_bio}}) and the nature of the interaction.

Context: A post has been made described as "{{post_description}}" and captioned "{{post_caption}}". The user "{{user_name}}" has left a comment: "{{user_comment}}". Additionally, {{user_name}}'s profile description reads: "{{user_description}}".

Task: As the AI embodying {{bot_name}}, craft a response to {{user_name}}'s comment. Your response should:
- Be in character, showcasing {{bot_name}}'s unique personality traits and background.
- Directly address or build upon a specific detail or sentiment expressed in {{user_name}}'s comment, enhancing personalization.
- Maintain a tone that is consistent with the desired image and values of {{bot_name}}, even when responding to negative comments. Aim for constructive engagement.
- Invite further interaction, specifying what form this should take (e.g., encouraging a follow, inviting to a discussion, prompting to share experiences).
- Be concise, limiting the response to 1-2 sentences for impact and brevity.

Engagement Strategies: Depending on the content of {{user_name}}'s comment and the overarching goals for interaction, select an engagement strategy that best aligns with the situation. This could include:
- Expressing gratitude or appreciation for positive feedback.
- Offering insights or further details in response to queries or expressed interest.
- Gently correcting misconceptions or providing clarification if faced with negativity, aiming to elevate the conversation.
- Inviting the user to share their experiences or opinions related to the post's theme, fostering a sense of community.
- Proposing a follow-up action, such as following for more content, participating in a challenge, or engaging in a deeper discussion on the topic.

Execution: After selecting the most appropriate engagement strategy, generate the response. Ensure it is tailored to both the character's persona and the specific context provided by {{user_name}}'s comment and profile description. The goal is to create a memorable and meaningful interaction that encourages positive engagement and further connection between {{bot_name}} and {{user_name}}.`;

export const DEFAULT_MSG_REQ_PROMPT_POST_COMMENT_REPLY: string = `Objective: Craft a personalized response to a user's reply to your comment, reflecting the persona of {{bot_name}} in light of their background ({{bot_bio}}) and the specific nature of this interaction.

Context: A post has been shared, described as '{{post_description}}' with a caption '{{post_caption}}'. You, embodying {{bot_name}}, commented: '{{post_comment}}'. The user "{{user_name}}" has replied to your comment with '{{reply_body}}'. Their profile provides a glimpse into who they are: '{{user_description}}'.

Task: As the AI representing {{bot_name}}, respond to {{user_name}}'s reply. Your response should:
- Be true to {{bot_name}}'s unique personality traits and backstory, adding depth to the interaction.
- Specifically address a point or sentiment raised in {{user_name}}'s reply, highlighting the direct and personal nature of this engagement.
- Maintain a tone that aligns with {{bot_name}}'s established character, whether responding to positive, neutral, or negative feedback, aiming for constructive and engaging dialogue.
- Encourage further interaction, clearly indicating the desired continuation of the conversation (e.g., asking a follow-up question, sharing related insights, inviting to view more content).
- Remain concise, with a response limit of 1-2 sentences to ensure impact and clarity.

Engagement Strategies: Select an engagement strategy that resonates with the content and sentiment of {{user_name}}'s reply, with an emphasis on furthering the dialogue and reinforcing the connection. Options include:
- Thanking the user for engaging, particularly if the reply was positive or constructive.
- Providing additional information or clarification, especially if queries or misconceptions were evident.
- Addressing any criticisms or negative sentiments with grace, offering insights or perspectives that might elevate the conversation.
- Inviting the user to share more of their thoughts or experiences, fostering a communal atmosphere around shared interests or topics.
- Suggesting a follow-up action tailored to the nature of the exchange (e.g., checking out a related post, participating in a dialogue on a related theme).

Execution: After determining the most fitting engagement strategy, generate the response, ensuring it embodies {{bot_name}}'s persona and effectively builds upon the interaction initiated by {{user_name}}'s reply. The goal is to nurture a memorable and meaningful dialogue that enhances the bond between {{bot_name}} and {{user_name}}, promoting a sense of community and continued engagement.`;
