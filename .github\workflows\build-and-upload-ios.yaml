name: Build iOS and upload to TestFlight

on:
  workflow_dispatch:
  workflow_call:
    inputs:
      commit_long_sha:
        required: true
        type: string
    secrets:
      APP_STORE_CONNECT_KEY_ID:
        required: true
      APP_STORE_CONNECT_ISSUER_ID:
        required: true
      APP_STORE_CONNECT_KEY_BASE64:
        required: true
      APPLE_DISTRIBUTION_CERTIFICATE_BASE64:
        required: true
      APPLE_DISTRIBUTION_CERTIFICATE_PASSWORD:
        required: true

concurrency:
  group: ios-${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-and-upload-ios:
    runs-on: macOS-latest
    defaults:
      run:
        working-directory: ./packages/mobile
    steps:
      - name: output ref that will be used in the next step
        working-directory: .
        run: |
          echo "inputs.commit_long_sha: ${{ inputs.commit_long_sha }}"
          echo "github.head_ref: ${{ github.head_ref }}"
          echo "resulting ref: ${{ inputs.commit_long_sha || github.head_ref }}"

      - uses: actions/checkout@v4
        with:
          ref: ${{ inputs.commit_long_sha || github.head_ref }}

      - name: Use Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: npm

      - name: Install dependencies
        run: pushd ../.. && npm install && popd

      - name: Read build version
        id: read-build-version
        run: echo "BUILD_VERSION=$(npm run --silent set-version -- --read-build-version)" >> $GITHUB_OUTPUT

      - name: output build version in a separate step
        run: 'echo "read build version: ${{ steps.read-build-version.outputs.BUILD_VERSION }}"'

      - uses: actions/cache@v4
        with:
          path: ./packages/mobile/ios/App/Pods
          key: ${{ runner.os }}-pods-${{ hashFiles('**/Podfile.lock') }}
          restore-keys: |
            ${{ runner.os }}-pods-

      - name: Ionic Capacitor Sync
        run: npm run sync

      - name: Ionic Capacitor Update
        run: npm run cap-update ios

      - uses: ruby/setup-ruby@v1
        with:
          working-directory: "./packages/mobile"
          ruby-version: "3.0"
          bundler-cache: true

      - uses: maierj/fastlane-action@v3.1.0
        env:
          APPLE_DISTRIBUTION_CERTIFICATE_BASE64: ${{ secrets.APPLE_DISTRIBUTION_CERTIFICATE_BASE64 }}
          APPLE_DISTRIBUTION_CERTIFICATE_PASSWORD: ${{ secrets.APPLE_DISTRIBUTION_CERTIFICATE_PASSWORD }}
          APP_STORE_CONNECT_KEY_ID: ${{ secrets.APP_STORE_CONNECT_KEY_ID }}
          APP_STORE_CONNECT_ISSUER_ID: ${{ secrets.APP_STORE_CONNECT_ISSUER_ID }}
          APP_STORE_CONNECT_KEY_BASE64: ${{ secrets.APP_STORE_CONNECT_KEY_BASE64 }}
          BUILD_VERSION: ${{ steps.read-build-version.outputs.BUILD_VERSION }}
          GCS_SERVICE_ACCOUNT_KEY_BASE64: ${{ secrets.GCS_SERVICE_ACCOUNT_KEY_BASE64 }}
          GCS_BUCKET: ${{ vars.GCS_BUCKET }}
          GCS_PROJECT: ${{ vars.GCS_PROJECT }}
        with:
          subdirectory: "./packages/mobile"
          lane: ios beta

      - name: Upload release bundle
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: ios-artifacts
          path: ./packages/mobile/artifacts
          if-no-files-found: error
          retention-days: 10
